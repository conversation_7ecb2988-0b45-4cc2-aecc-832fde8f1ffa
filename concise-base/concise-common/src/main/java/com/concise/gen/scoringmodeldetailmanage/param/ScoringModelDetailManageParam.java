package com.concise.gen.scoringmodeldetailmanage.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import java.util.List;

/**
* 评分模型详情参数类
 *
 * <AUTHOR>
 * @date 2022-05-12 22:52:05
*/
@Data
public class ScoringModelDetailManageParam extends BaseParam {

    /**
     * ID
     */
    private String id;

    /**
     * 上级ID
     */
    private String topId;

    /**
     * 评分模型ID
     */
    private String scoringModelId;

    /**
     * 细则名称
     */
    private String detailName;

    /**
     * 指标分值
     */
    private Integer indexPoint;

    /**
     * 子节点
     */
    private List<ScoringModelDetailManageParam> children;

    /**
     *
     */
    private String extend01;

    /**
     *
     */
    private String extend02;

    /**
     *
     */
    private String extend03;

}
