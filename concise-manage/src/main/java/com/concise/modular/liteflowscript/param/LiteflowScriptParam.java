package com.concise.modular.liteflowscript.param;

import com.concise.common.pojo.base.param.BaseParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 规则引擎脚本参数类
 *
 * <AUTHOR>
 * @date 2025-05-08 16:44:31
*/
@Data
public class LiteflowScriptParam extends BaseParam {

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotBlank(message = "不能为空，请检查applicationName参数", groups = {add.class, edit.class})
    private String applicationName;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotBlank(message = "不能为空，请检查scriptId参数", groups = {add.class, edit.class})
    private String scriptId;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotBlank(message = "不能为空，请检查scriptName参数", groups = {add.class, edit.class})
    private String scriptName;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotBlank(message = "不能为空，请检查scriptData参数", groups = {add.class, edit.class})
    private String scriptData;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotBlank(message = "不能为空，请检查scriptType参数", groups = {add.class, edit.class})
    private String scriptType;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotBlank(message = "不能为空，请检查language参数", groups = {add.class, edit.class})
    private String language;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空，请检查enable参数", groups = {add.class, edit.class})
    private Integer enable;

}
