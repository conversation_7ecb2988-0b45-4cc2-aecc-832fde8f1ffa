package com.concise.gen.scoringmodeldetailmanageportrayal.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.scoringmodeldetailmanage. param.ScoringModelDetailManageParam;
import com.concise.gen.scoringmodeldetailmanage. service.ScoringModelDetailManageService;
import com.concise.gen.scoringmodeldetailmanageportrayal.param.ScoringModelDetailManagePortrayalParam;
import com.concise.gen.scoringmodeldetailmanageportrayal.service.ScoringModelDetailManagePortrayalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 评分模型详情控制器
 *
 * <AUTHOR>
 * @date 2022-05-12 22:52:05
 */
@Api(tags = "评分模型详情(精准画像)")
@RestController
public class ScoringModelDetailManagePortrayalController {

    @Resource
    private ScoringModelDetailManagePortrayalService scoringModelDetailManagePortrayalService;

    /**
     * 查询评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    @Permission
    @GetMapping("/scoringModelDetailManagePortrayal/page")
    @ApiOperation("评分模型详情_分页查询")
    @BusinessLog(title = "评分模型详情_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(ScoringModelDetailManagePortrayalParam scoringModelDetailManagePortrayalParam) {
        return new SuccessResponseData(scoringModelDetailManagePortrayalService.page(scoringModelDetailManagePortrayalParam));
    }

    /**
     * 添加评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    @Permission
    @PostMapping("/scoringModelDetailManagePortrayal/add")
    @ApiOperation("评分模型详情_增加")
    @BusinessLog(title = "评分模型详情_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(ScoringModelDetailManagePortrayalParam.add.class) ScoringModelDetailManagePortrayalParam scoringModelDetailManagePortrayalParam) {
        scoringModelDetailManagePortrayalService.add(scoringModelDetailManagePortrayalParam);
        return new SuccessResponseData();
    }

    /**
     * 删除评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    @Permission
    @PostMapping("/scoringModelDetailManagePortrayal/delete")
    @ApiOperation("评分模型详情_删除")
    @BusinessLog(title = "评分模型详情_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(ScoringModelDetailManagePortrayalParam.delete.class) ScoringModelDetailManagePortrayalParam scoringModelDetailManagePortrayalParam) {
        scoringModelDetailManagePortrayalService.delete(scoringModelDetailManagePortrayalParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    @Permission
    @PostMapping("/scoringModelDetailManagePortrayal/edit")
    @ApiOperation("评分模型详情_编辑")
    @BusinessLog(title = "评分模型详情_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(ScoringModelDetailManagePortrayalParam.edit.class) ScoringModelDetailManagePortrayalParam scoringModelDetailManagePortrayalParam) {
        scoringModelDetailManagePortrayalService.edit(scoringModelDetailManagePortrayalParam);
        return new SuccessResponseData();
    }

    /**
     * 查看评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    @Permission
    @GetMapping("/scoringModelDetailManagePortrayal/detail")
    @ApiOperation("评分模型详情_查看")
    @BusinessLog(title = "评分模型详情_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(ScoringModelDetailManagePortrayalParam.detail.class) ScoringModelDetailManagePortrayalParam scoringModelDetailManagePortrayalParam) {
        return new SuccessResponseData(scoringModelDetailManagePortrayalService.detail(scoringModelDetailManagePortrayalParam));
    }

    /**
     * 评分模型详情列表
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    @Permission
    @GetMapping("/scoringModelDetailManagePortrayal/list")
    @ApiOperation("评分模型详情_列表")
    @BusinessLog(title = "评分模型详情_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(ScoringModelDetailManagePortrayalParam scoringModelDetailManagePortrayalParam) {
        return new SuccessResponseData(scoringModelDetailManagePortrayalService.list(scoringModelDetailManagePortrayalParam));
    }

}
