package com.concise.sys.modular.dict.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 系统字典值参数
 *
 * <AUTHOR>
 * @date 2020/3/31 20:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysDictDataParam extends BaseParam {

    /**
     * 主键
     */
    private Long id;

    /**
     * 字典类型id
     */
    private Long typeId;

    /**
     * 值
     */
    private String value;

    /**
     * 编码
     */
    private String code;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态（字典 0正常 1停用 2删除）
     */
    private Integer status;

    /**
     * 字典类型name
     */
    private String typeName;
}
