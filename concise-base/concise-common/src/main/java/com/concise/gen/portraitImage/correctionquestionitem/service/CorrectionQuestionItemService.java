package com.concise.gen.portraitImage.correctionquestionitem.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.portraitImage.correctionquestionitem.entity.CorrectionQuestionItem;
import com.concise.gen.portraitImage.correctionquestionitem.param.CorrectionQuestionItemParam;

import java.util.List;

/**
 * 量表配置--试题选项表service接口
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:38
 */
public interface CorrectionQuestionItemService extends IService<CorrectionQuestionItem> {

    /**
     * 查询量表配置--试题选项表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:38
     */
    PageResult<CorrectionQuestionItem> page(CorrectionQuestionItemParam correctionQuestionItemParam);

    /**
     * 量表配置--试题选项表列表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:38
     */
    List<CorrectionQuestionItem> list(CorrectionQuestionItemParam correctionQuestionItemParam);

    /**
     * 添加量表配置--试题选项表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:38
     */
    void add(CorrectionQuestionItemParam correctionQuestionItemParam);

    /**
     * 删除量表配置--试题选项表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:38
     */
    void delete(CorrectionQuestionItemParam correctionQuestionItemParam);

    /**
     * 编辑量表配置--试题选项表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:38
     */
    void edit(CorrectionQuestionItemParam correctionQuestionItemParam);

    /**
     * 查看量表配置--试题选项表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:38
     */
     CorrectionQuestionItem detail(CorrectionQuestionItemParam correctionQuestionItemParam);
}
