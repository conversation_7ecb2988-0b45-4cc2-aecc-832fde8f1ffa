package com.concise.gen.questioncategory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.questioncategory.entity.CateTree;
import com.concise.gen.questioncategory.entity.QuestionCategory;
import com.concise.gen.questioncategory.enums.QuestionCategoryExceptionEnum;
import com.concise.gen.questioncategory.mapper.QuestionCategoryMapper;
import com.concise.gen.questioncategory.param.QuestionCategoryParam;
import com.concise.gen.questioncategory.service.QuestionCategoryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 题库管理service接口实现类
 *
 * <AUTHOR>
 * @date 2023-01-03 16:40:11
 */
@Service
public class QuestionCategoryServiceImpl extends ServiceImpl<QuestionCategoryMapper, QuestionCategory> implements QuestionCategoryService {

    @Override
    public PageResult<QuestionCategory> page(QuestionCategoryParam questionCategoryParam) {
        QueryWrapper<QuestionCategory> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(questionCategoryParam)) {

            // 根据父分类id 查询
            if (ObjectUtil.isNotEmpty(questionCategoryParam.getParentId())) {
                queryWrapper.lambda().eq(QuestionCategory::getParentId, questionCategoryParam.getParentId());
            }
            // 根据分类名 查询
            if (ObjectUtil.isNotEmpty(questionCategoryParam.getTypeName())) {
                queryWrapper.lambda().eq(QuestionCategory::getTypeName, questionCategoryParam.getTypeName());
            }
            // 根据部门id 查询
            if (ObjectUtil.isNotEmpty(questionCategoryParam.getDepId())) {
                queryWrapper.lambda().eq(QuestionCategory::getDepId, questionCategoryParam.getDepId());
            }
            // 根据排序 查询
            if (ObjectUtil.isNotEmpty(questionCategoryParam.getIndexId())) {
                queryWrapper.lambda().eq(QuestionCategory::getIndexId, questionCategoryParam.getIndexId());
            }
            // 根据分类等级 查询
            if (ObjectUtil.isNotEmpty(questionCategoryParam.getRank())) {
                queryWrapper.lambda().eq(QuestionCategory::getRank, questionCategoryParam.getRank());
            }
            // 根据是否删除 查询
            if (ObjectUtil.isNotEmpty(questionCategoryParam.getIsDel())) {
                queryWrapper.lambda().eq(QuestionCategory::getIsDel, questionCategoryParam.getIsDel());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(questionCategoryParam.getCreateBy())) {
                queryWrapper.lambda().eq(QuestionCategory::getCreateBy, questionCategoryParam.getCreateBy());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(questionCategoryParam.getUpdateBy())) {
                queryWrapper.lambda().eq(QuestionCategory::getUpdateBy, questionCategoryParam.getUpdateBy());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<QuestionCategory> list(QuestionCategoryParam questionCategoryParam) {
        return this.list();
    }

    @Override
    public void add(QuestionCategoryParam questionCategoryParam) {
        QuestionCategory questionCategory = new QuestionCategory();
        BeanUtil.copyProperties(questionCategoryParam, questionCategory);
        questionCategory.setIsDel("0");
        questionCategory.setCreateTime(DateUtil.date());
        this.save(questionCategory);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(QuestionCategoryParam questionCategoryParam) {
        QuestionCategory questionCategory = new QuestionCategory();
        BeanUtils.copyProperties(questionCategoryParam,questionCategory);
        questionCategory.setIsDel("1");
        this.updateById(questionCategory);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(QuestionCategoryParam questionCategoryParam) {
        QuestionCategory questionCategory = this.queryQuestionCategory(questionCategoryParam);
        BeanUtil.copyProperties(questionCategoryParam, questionCategory);
        questionCategory.setUpdateTime(DateUtil.date());
        this.updateById(questionCategory);
    }

    @Override
    public QuestionCategory detail(QuestionCategoryParam questionCategoryParam) {
        return this.queryQuestionCategory(questionCategoryParam);
    }

    /**
     * 获取题库管理
     *
     * <AUTHOR>
     * @date 2023-01-03 16:40:11
     */
    private QuestionCategory queryQuestionCategory(QuestionCategoryParam questionCategoryParam) {
        QuestionCategory questionCategory = this.getById(questionCategoryParam.getId());
        if (ObjectUtil.isNull(questionCategory)) {
            throw new ServiceException(QuestionCategoryExceptionEnum.NOT_EXIST);
        }
        return questionCategory;
    }

    @Override
    public List<CateTree> getCateTree() {
        QueryWrapper<QuestionCategory> wrapper = new QueryWrapper<>();
        wrapper.eq("is_del","0");
        List<CateTree> parentList = new ArrayList<>();
        List<QuestionCategory> categories=this.list(wrapper);

        categories.forEach(category -> {
            if (StringUtils.isEmpty(category.getParentId())){
                CateTree cateTree = new CateTree();
                BeanUtils.copyProperties(category,cateTree);
                parentList.add(cateTree);
            }
        });
        //去掉所有一级分类
        categories.removeAll(parentList);
        Collections.sort(parentList, new Comparator<CateTree>() {
            @Override
            public int compare(CateTree o1, CateTree o2) {
                //按照排序字段升序排序
                if (o1.getIndexId()>o2.getIndexId()){
                    return 1;
                }
                if (o1.getIndexId().equals(o2.getIndexId())){
                    return 0;
                }
                return -1;
            }
        });
        getTree(parentList,categories);
        return parentList;
    }

    private void getTree(List<CateTree> parentList, List<QuestionCategory> categories) {
        for (CateTree cateTree : parentList) {
            List<CateTree> childList = new ArrayList<>();
            for (QuestionCategory category1 : categories) {
                if (category1.getParentId().equals(cateTree.getId())){
                    CateTree cateTree1 = new CateTree();
                    BeanUtils.copyProperties(category1,cateTree1);
                    childList.add(cateTree1);
                }
            }
            if (childList.size()>0){
                getTree(childList,categories);
                Collections.sort(childList, new Comparator<CateTree>() {
                    @Override
                    public int compare(CateTree o1, CateTree o2) {
                        //按照排序字段升序排序
                        if (o1.getIndexId()>o2.getIndexId()){
                            return 1;
                        }
                        if (o1.getIndexId().equals(o2.getIndexId())){
                            return 0;
                        }
                        return -1;
                    }
                });
                cateTree.setChildren(childList);
            }
        }
    }
}
