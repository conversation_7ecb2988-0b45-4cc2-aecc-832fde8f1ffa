package com.concise.modular.llm.config;

import com.concise.modular.llm.entity.LlmConfig;
import com.concise.modular.llm.service.LlmConfigService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 通义千问配置类
 */
@Data
@Component
public class TongYiQianWenConfig {

    @Autowired
    private LlmConfigService llmConfigService;

    /**
     * 获取所有可用的模型配置
     */
    public List<LlmModelConfig> getModels() {
        List<LlmConfig> dbConfigs = llmConfigService.lambdaQuery()
                .eq(LlmConfig::getStatus, 1)
                .list();
        
        List<LlmModelConfig> models = new ArrayList<>();
        for (LlmConfig config : dbConfigs) {
            models.add(LlmModelConfig.fromDbConfig(config));
        }
        return models;
    }

    /**
     * 获取默认模型配置
     */
    public LlmModelConfig getDefaultModelConfig() {
        LlmConfig config = llmConfigService.getDefaultConfig();
        if (config == null) {
            throw new RuntimeException("未配置默认模型");
        }
        return LlmModelConfig.fromDbConfig(config);
    }

    /**
     * 根据模型名称获取模型配置
     * @param modelName 模型名称
     * @return 模型配置
     */
    public LlmModelConfig getModelConfig(String modelName) {
        if (modelName == null || modelName.isEmpty()) {
            return getDefaultModelConfig();
        }
        
        LlmConfig config = llmConfigService.getConfigByModelName(modelName);
        if (config == null) {
            throw new RuntimeException("未找到模型配置：" + modelName);
        }
        return LlmModelConfig.fromDbConfig(config);
    }
}
