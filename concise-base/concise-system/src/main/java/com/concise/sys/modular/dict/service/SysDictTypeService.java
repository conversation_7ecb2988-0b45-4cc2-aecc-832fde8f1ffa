package com.concise.sys.modular.dict.service;

import cn.hutool.core.lang.Dict;
import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.sys.modular.dict.entity.DictModel;
import com.concise.sys.modular.dict.entity.SysDictType;
import com.concise.sys.modular.dict.param.SysDictTypeParam;
import com.concise.sys.modular.dict.result.SysDictTreeNode;

import java.util.List;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

/**
 * 系统字典类型service接口
 *
 * <AUTHOR>
 * @date 2020/3/13 16:10
 */
public interface SysDictTypeService extends IService<SysDictType> {

    /**
     * 查询系统字典类型
     *
     * @param sysDictTypeParam 查询参数
     * @return 查询分页结果
     * <AUTHOR>
     * @date 2020/3/31 20:34
     */
    PageResult<SysDictType> page(SysDictTypeParam sysDictTypeParam);

    /**
     * 获取字典类型列表
     *
     * @param sysDictTypeParam 查询参数
     * @return 系统字典类型列表
     * <AUTHOR>
     * @date 2020/3/31 21:07
     */
    List<SysDictType> list(SysDictTypeParam sysDictTypeParam);

    /**
     * 系统字典类型下拉
     *
     * @param sysDictTypeParam 下拉参数
     * @return 增强版hashMap，格式：[{"code:":"1", "value":"正常"}]
     * <AUTHOR>
     * @date 2020/3/31 21:23
     */
    List<Dict> dropDown(SysDictTypeParam sysDictTypeParam);

    /**
     * 添加系统字典类型
     *
     * @param sysDictTypeParam 添加参数
     * <AUTHOR>
     * @date 2020/3/31 20:35
     */
    void add(SysDictTypeParam sysDictTypeParam);

    /**
     * 删除系统字典类型
     *
     * @param sysDictTypeParam 删除参数
     * <AUTHOR>
     * @date 2020/3/31 20:35
     */
    void delete(SysDictTypeParam sysDictTypeParam);

    /**
     * 编辑系统字典类型
     *
     * @param sysDictTypeParam 编辑参数
     * <AUTHOR>
     * @date 2020/3/31 20:35
     */
    void edit(SysDictTypeParam sysDictTypeParam);

    /**
     * 查看系统字典类型
     *
     * @param sysDictTypeParam 查看参数
     * @return 系统字典类型
     * <AUTHOR>
     * @date 2020/3/31 20:35
     */
    SysDictType detail(SysDictTypeParam sysDictTypeParam);

    /**
     * 修改状态（字典 0正常 1停用 2删除）
     *
     * @param sysDictTypeParam 修改参数
     * <AUTHOR>
     * @date 2020/4/30 22:30
     */
    void changeStatus(SysDictTypeParam sysDictTypeParam);

    /**
     * 系统字典类型与字典值构造的树
     *
     * @return 树
     * <AUTHOR>
     * @date 2020/4/30 22:30
     */
    List<SysDictTreeNode> tree();

    Map<String, List<DictModel>> queryManyDictByKeys(List<String> dictCodeList, List<String> values);

    List<DictModel> queryTableDictTextByKeys(String table, String text, String code, List<String> asList);

    /**
     * 导入指定的excel
     *
     * @param file excel文件
     */
    void importExcel(MultipartFile file);
}
