package com.concise.gen.secondaryindexmanageportrayal.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
* 二级指标管理信息表参数类
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:21
*/
@Data
public class SecondaryIndexManagePortrayalParam extends BaseParam {

    /**
     * 二级指标ID
     */
    @NotNull(message = "二级指标ID不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 上级指标ID
     */
    @NotBlank(message = "上级指标ID不能为空，请检查topindexid参数", groups = {add.class, edit.class})
    private String topindexid;

    /**
     * 指标名称
     */
    @NotBlank(message = "指标名称不能为空，请检查indexName参数", groups = {add.class, edit.class})
    private String indexName;

    /**
     * 排序
     */
    @NotBlank(message = "排序不能为空，请检查sort参数", groups = {add.class, edit.class})
    private String sort;

    /**
     * 指标分值
     */
    private int indexPoint;

    /**
     * 评分占比
     */
    private int percent;

    /**
     * 删除状态
     */
    private String delFlag;
    /**
     *
     */
    private String extend01;

    /**
     *
     */
    private String extend02;

    /**
     *
     */
    private String extend03;

    private List<SecondaryIndexManagePortrayalParam> children;


}
