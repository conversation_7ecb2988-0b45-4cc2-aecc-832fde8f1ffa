package com.concise.gen.taskinfo.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.common.pojo.node.AntdBaseTreeNode;
import com.concise.gen.taskinfo.entity.CorrectionSecConfig;
import com.concise.gen.taskinfo.entity.TaskInfoDetail;
import com.concise.gen.taskinfo.result.TaskInfoDetailResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 任务详情表
 *
 * <AUTHOR>
 * @date 2022-02-28 15:17:32
 */
public interface TaskInfoDetailMapper extends BaseMapper<TaskInfoDetail> {

    /**
     *
     * @param queryWrapper
     * @return
     */
    TaskInfoDetailResult taskCount(@Param("ew") QueryWrapper queryWrapper);

    Map<String, Object> taskStatus(@Param("ew") QueryWrapper queryWrapper);

    TaskInfoDetail getDetail(String tel,String taskName);
    TaskInfoDetail getDetailByCallId(String callId);

    List<AntdBaseTreeNode> getKeyPeriod();

    List<CorrectionSecConfig> getPointList();
}
