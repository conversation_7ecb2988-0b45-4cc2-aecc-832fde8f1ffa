package com.concise.sys.core.mybatis.fieldfill;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.log.Log;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.concise.core.context.login.LoginContextHolder;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.ReflectionException;
import com.concise.core.login.SysLoginUser;

import java.util.Date;

/**
 * 自定义sql字段填充器，自动填充创建修改相关字段
 *
 * <AUTHOR>
 * @date 2020/3/30 15:21
 */
public class CustomMetaObjectHandler implements MetaObjectHandler {

    private static final Log log = Log.get();

    private static final String CREATE_USER = "createUser";

    private static final String CREATE_TIME = "createTime";

    private static final String UPDATE_USER = "updateUser";

    private static final String UPDATE_TIME = "updateTime";

    @Override
    public void insertFill(MetaObject metaObject) {
        try {
            Long userId = this.getUserUniqueId();
            Date currentTime = new Date();


            //为空则设置createUser（BaseEntity)
            Object createUser = metaObject.getValue(CREATE_USER);
            if(ObjectUtil.isNull(createUser)) {
                setFieldValByName(CREATE_USER, userId, metaObject);
            }

            //为空则设置createTime（BaseEntity)
            Object createTime = metaObject.getValue(CREATE_TIME);
            if(ObjectUtil.isNull(createTime)) {
                setFieldValByName(CREATE_TIME, currentTime, metaObject);
            }

            //插入时也设置updateUser和updateTime
            setFieldValByName(UPDATE_USER, userId, metaObject);
            setFieldValByName(UPDATE_TIME, currentTime, metaObject);

        } catch (ReflectionException e) {

        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        try {
            Long userId = this.getUserUniqueId();
            Date currentTime = new Date();


            //设置updateUser（BaseEntity)
            setFieldValByName(UPDATE_USER, userId, metaObject);
            //设置updateTime（BaseEntity)
            setFieldValByName(UPDATE_TIME, currentTime, metaObject);

        } catch (ReflectionException e) {
        }
    }

    /**
     * 获取用户唯一id
     */
    private Long getUserUniqueId() {
        try {
            SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUserWithoutException();
            if(ObjectUtil.isNotNull(sysLoginUser)) {
                // 将String类型的ID转换为Long类型
                String userId = sysLoginUser.getId();
                return userId != null ? Long.valueOf(userId) : null;
            } else {
                return null;
            }
        } catch (Exception e) {
            //如果获取不到就返回null
            return null;
        }
    }
}
