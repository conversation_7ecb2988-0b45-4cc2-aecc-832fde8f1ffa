package com.concise.modular.controller;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.export.styler.ExcelExportStylerDefaultImpl;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.common.util.HttpServletUtil;
import com.concise.common.util.PoiUtil;
import com.concise.modular.model.InvoiceModel;
import com.concise.modular.model.StringModel;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Workbook;
import org.ofdrw.reader.ContentExtractor;
import org.ofdrw.reader.OFDReader;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
public class SuFangTestController {


    @GetMapping("/loadPDFTest1")
    public void loadPDF(String filePath) throws IOException {
        //需要转到数据库中的pdf文件位置
        filePath = "D:\\tempFilePath\\";//D:\tempFilePath
        File file = new File(filePath);
        // List<OssFileBak> ossFiles = new ArrayList<>();
        File[] files = file.listFiles();
        PDFTextStripper pdfStripper = null;
        try {
            pdfStripper = new PDFTextStripper();
        } catch (IOException e) {
            e.printStackTrace();
        }
        List<InvoiceModel> invoiceModels = new ArrayList<>();
        InvoiceModel invoiceModel;
        for (int i = 0; i < files.length; i++) {
//            OssFileBak ossFileBak = new OssFileBak();
//            ossFileBak.setFileName(PDFFile.getName());
            //兼容ofd文件
            if (files[i].getName().indexOf(".pdf") != -1 || files[i].getName().indexOf(".PDF") != -1) {

                PDDocument document = null;

                try {
                    document = PDDocument.load(files[i]);
                    // ossFileBak.setFileContent(pdfStripper.getText(document));
                } catch (IOException e) {
                    e.printStackTrace();
                    throw new RuntimeException(files[i].getName() + "解析异常！");
                }
                //暂时性随时删除
//                ossFileBak.setId(1);
//                ossFileBak.setFileStatus(1);
                //  ossFiles.add(ossFileBak);
            }

            //如果为ofd文件特殊处理
            if (files[i].getName().indexOf(".ofd") != -1 || files[i].getName().indexOf(".OFD") != -1) {
                OFDReader reader = null;
                try {
                    //读ofd文件
                    reader = new OFDReader(files[i].getAbsolutePath());
                } catch (IOException e) {
                    e.printStackTrace();
                }
                //提取文字
                ContentExtractor extractor = new ContentExtractor(reader);

                List<String> pageContent = extractor.extractAll();

                invoiceModel = new InvoiceModel();
//                StringBuffer stringBuffer = new StringBuffer();
                //循环提取
                for (int j = 0; j < pageContent.size(); j++) {
//                    invoiceModel.setXh(String.valueOf(i + 1));
//                    invoiceModel.setSbh(pageContent.get(3));
//                    invoiceModel.setNd(pageContent.get(14));
//                    invoiceModel.setJysx(pageContent.get(25));
//                    invoiceModel.setKpfmc(pageContent.get(9));
//                    invoiceModel.setFphm(pageContent.get(6));
//                    invoiceModel.setKjrq(pageContent.get(14));
                    invoiceModel.setWjm(files[i].getName());
                }
                invoiceModels.add(invoiceModel);
                //添加测试随时修改
//                ossFileBak.setId(1);
//                ossFileBak.setFileStatus(1);
//                ossFileBak.setFileContent(stringBuffer.toString());
                //               ossFiles.add(ossFileBak);
            }
            //  FileUtil.del(file);
        }
        PoiUtil.exportExcelWithStream("发票信息提取-" + DateUtil.now() + ".xls", InvoiceModel.class, invoiceModels);
        FileUtil.del(file);
        // ossFileBakMapper.insertList(ossFiles);
    }

    @GetMapping("/loadPDFTest")
    public void test(String filePath) throws Exception {
        //需要转到数据库中的pdf文件位置
        if (System.getProperty("os.name").toLowerCase().contains("win")) {
            filePath = "D:\\tempFilePath\\";
        } else {
            filePath = "/usr/local/tempFilePath/";
        }//D:\tempFilePath
        File file = new File(filePath);
        // List<OssFileBak> ossFiles = new ArrayList<>();
        File[] files = file.listFiles();
        PDFTextStripper pdfStripper = null;
        pdfStripper = new PDFTextStripper();
        InvoiceModel invoiceModel;
        // 多个sheet配置参数
        final List<Map<String, Object>> sheetsList = Lists.newArrayList();
        List<InvoiceModel> zyfp = new ArrayList();
        List<InvoiceModel> dzpp = new ArrayList();
        List<InvoiceModel> dzzp = new ArrayList();
        List<StringModel> errorList = new ArrayList();
        for (int i = 0; i < files.length; i++) {
            try {
                //兼容ofd文件
                if (files[i].getName().indexOf(".pdf") != -1 || files[i].getName().indexOf(".PDF") != -1) {
                    PDDocument document = null;
                    document = PDDocument.load(files[i]);
                    String text = pdfStripper.getText(document);
                    text = null;
                }
                //如果为ofd文件特殊处理
                if (files[i].getName().indexOf(".ofd") != -1 || files[i].getName().indexOf(".OFD") != -1) {
                    OFDReader reader = null;
                    //读ofd文件
                    String absolutePath = files[i].getAbsolutePath();
                    reader = new OFDReader(absolutePath);
                    //提取文字
                    ContentExtractor extractor = new ContentExtractor(reader);
                    List<String> pageContent = extractor.extractAll();
                    invoiceModel = new InvoiceModel();
                    //循环提取
                    for (int j = 0; j < pageContent.size(); j++) {

                        invoiceModel.setWjm(files[i].getName());
                        if (pageContent.get(0).equals("福建增值税电子专用发票")) {
                            invoiceModel.setXh(String.valueOf(zyfp.size() + 1));
                            invoiceModel.setSbh(pageContent.get(3));
                            invoiceModel.setNd(pageContent.get(14));
                            invoiceModel.setJysx(pageContent.get(25));
                            invoiceModel.setKpfmc(pageContent.get(9));
                            invoiceModel.setFphm(pageContent.get(6));
                            invoiceModel.setKjrq(pageContent.get(14));
                        } else if (pageContent.get(0).equals("江苏增值税电子专用发票")) {
                            invoiceModel.setXh(String.valueOf(zyfp.size() + 1));
                            invoiceModel.setSbh(pageContent.get(3));
                            invoiceModel.setNd(pageContent.get(15));
                            invoiceModel.setJysx(pageContent.get(26));
                            invoiceModel.setKpfmc(pageContent.get(9));
                            invoiceModel.setFphm(pageContent.get(6));
                            invoiceModel.setKjrq(pageContent.get(15));
                        } else if (pageContent.size() == 21) {
                            invoiceModel.setXh(String.valueOf(dzpp.size() + 1));
                            invoiceModel.setSbh(pageContent.get(3));
                            invoiceModel.setNd(pageContent.get(1));
                            invoiceModel.setJysx(pageContent.get(15));
                            invoiceModel.setKpfmc(pageContent.get(4));
                            invoiceModel.setFphm(pageContent.get(0));
                            invoiceModel.setKjrq(pageContent.get(1));
                        } else if (pageContent.size() == 22) {
                            invoiceModel.setXh(String.valueOf(dzzp.size() + 1));
                            invoiceModel.setSbh(pageContent.get(3));
                            invoiceModel.setNd(pageContent.get(1));
                            invoiceModel.setJysx(pageContent.get(15));
                            invoiceModel.setKpfmc(pageContent.get(4));
                            invoiceModel.setFphm(pageContent.get(0));
                            invoiceModel.setKjrq(pageContent.get(1));
                        }
                        //
                    }
                    if (pageContent.get(0).equals("福建增值税电子专用发票")) {
                        zyfp.add(invoiceModel);
                    } else if (pageContent.get(0).equals("江苏增值税电子专用发票")) {
                        zyfp.add(invoiceModel);
                    } else if (pageContent.size() == 21) {
                        dzpp.add(invoiceModel);
                    } else if (pageContent.size() == 22) {
                        dzzp.add(invoiceModel);
                    }
                }
                //  FileUtil.del(file);
            } catch (Exception e) {
                StringModel stringModel = new StringModel();
                stringModel.setWjm(files[i].getName());
                errorList.add(stringModel);
                continue;
            }
        }
        List<String> sheetNameList = Lists.newArrayList("电子专用发票", "电子普票", "电子专票");
        if (errorList.size() > 0) {
            sheetNameList.add("未读取成功的文件");
        }
        for (String s : sheetNameList) {
            final String sheetName = s;
            Map<String, Object> exportMap = Maps.newHashMap();
            ExportParams exportParams = new ExportParams(null, null, sheetName);
            //exportParams.setStyle(ExcelSimpleStyleUtil.class);
            //以下3个参数为API中写死的参数名 分别是sheet配置/导出类（注解定义）/数据集
            exportMap.put("title", exportParams);
            if (s.equals("电子专用发票")) {
                exportMap.put("entity", InvoiceModel.class);
                exportMap.put("data", zyfp);
            } else if (s.equals("电子普票")) {
                exportMap.put("entity", InvoiceModel.class);
                exportMap.put("data", dzpp);
            } else if (s.equals("电子专票")) {
                exportMap.put("entity", InvoiceModel.class);
                exportMap.put("data", dzzp);
            } else if (s.equals("未读取成功的文件")) {
                exportMap.put("entity", StringModel.class);
                exportMap.put("data", errorList);
            }
            // 加入多sheet配置列表
            sheetsList.add(exportMap);
        }
        // 导出文件
        try {
            // 核心方法：导出含多个sheet的excel文件 【注意，该方法第二个参数必须使用ExcelType.HSSF，使用ExcelType.XSSF会报错!!!】
            final Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            String fileName = new String(("发票信息-" + DateUtil.now() + ".xls").getBytes("GBK"), StandardCharsets.ISO_8859_1);
            HttpServletResponse response = HttpServletUtil.getResponse();
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            OutputStream outputStream = response.getOutputStream();
            response.flushBuffer();
            workbook.write(outputStream);
            // 写完数据关闭流
            outputStream.close();

        } catch (IOException e) {
        }
        FileUtil.del(file);

    }

    private class ExcelSimpleStyleUtil extends ExcelExportStylerDefaultImpl {

        public ExcelSimpleStyleUtil(Workbook workbook) {
            super(workbook);
        }

        /**
         * 这里设置表头的格式，最上面的一行
         *
         * @see ExportParams#
         */
        @Override
        public CellStyle getHeaderStyle(short color) {
            CellStyle cellStyle = super.getHeaderStyle(color);
            cellStyle.setFont(getFont(workbook, 11, true));
            cellStyle.setFillBackgroundColor(HSSFColor.HSSFColorPredefined.GREY_50_PERCENT.getIndex());
            return cellStyle;
        }

        /**
         * 列标题
         */
        @Override
        public CellStyle getTitleStyle(short color) {
            CellStyle cellStyle = super.getTitleStyle(color);
            // 仅将表头的字体设置大一号且加粗
            cellStyle.setFont(getFont(workbook, 11, true));
            cellStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.LIGHT_TURQUOISE.getIndex());
            return cellStyle;
        }

        /*以下都是行样式，交替*/

        /**
         * 行样式
         */
        @Override
        public CellStyle stringSeptailStyle(Workbook workbook, boolean isWarp) {
            CellStyle cellStyle = super.stringSeptailStyle(workbook, isWarp);
            cellStyle.setFont(getFont(workbook, 10, false));
            return cellStyle;
        }

        /**
         * 这里设置循环行，没有样式的一行
         */
        @Override
        public CellStyle stringNoneStyle(Workbook workbook, boolean isWarp) {
            CellStyle cellStyle = super.stringNoneStyle(workbook, isWarp);
            cellStyle.setFont(getFont(workbook, 10, false));
            return cellStyle;
        }

        /**
         * 字体样式
         *
         * @param size   字体大小
         * @param isBold 是否加粗
         * @return
         */
        private Font getFont(Workbook workbook, int size, boolean isBold) {
            Font font = workbook.createFont();
            // 字体样式
            font.setFontName("微软雅黑");
            // 是否加粗
            font.setBold(isBold);
            // 字体大小
            font.setFontHeightInPoints((short) size);
            return font;
        }

    }


    @PostMapping("/uploadCategory")
    public ResponseData uploadCategoryLoadPDFTest(HttpServletRequest request, @RequestParam("file") MultipartFile[] files) {
        List<File> fileList = new ArrayList<>();
        File tempFile;
        createDir();
        for (MultipartFile file : files) {
            String fileName = file.getOriginalFilename();
            if (System.getProperty("os.name").toLowerCase().contains("win")) {
                tempFile = new File("D:\\tempFilePath\\" + fileName);
            } else {
                tempFile = new File("/usr/local/tempFilePath/" + fileName);
            }
            OutputStream out = null;
            try {
                //获取文件流，以文件流的方式输出到新文件
                out = new FileOutputStream(tempFile);
                byte[] ss = file.getBytes();
                for (int i = 0; i < ss.length; i++) {
                    out.write(ss[i]);
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (out != null) {
                    try {
                        out.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            fileList.add(tempFile);
        }
//        if (fileList.size()>0){
//            suFangTestService.uploadCategoryLoadPDFTest(fileList);
//        }
        return new SuccessResponseData();
    }


    // 生成文件夹
    private static void createDir() {
        // if 匹配win else linux
        String path = "";
        if (System.getProperty("os.name").toLowerCase().contains("win")) {
            path = "D:\\tempFilePath\\";
        } else {
            path = "/usr/local/tempFilePath/";
        }
        File folder = new File(path);
        if (!folder.exists() && !folder.isDirectory()) {
            folder.setWritable(true, false);
            folder.mkdirs();
            System.out.println("创建文件夹");
        } else {
            System.out.println("文件夹已存在");
        }
    }


}
