package com.concise.gen.scoringmodeldetailmanage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.scoringmodeldetailmanage.entity.ScoringModelDetailManage;
import com.concise.gen.scoringmodeldetailmanage.param.ScoringModelDetailManageParam;
import com.concise.gen.scoringmodelmanage.entity.ScoringModelManage;

import java.util.List;

/**
 * 评分模型详情service接口
 *
 * <AUTHOR>
 * @date 2022-05-12 22:52:05
 */
public interface ScoringModelDetailManageService extends IService<ScoringModelDetailManage> {

    /**
     * 查询评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    PageResult<ScoringModelDetailManage> page(ScoringModelDetailManageParam scoringModelDetailManageParam);

    /**
     * 评分模型详情列表
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    List<ScoringModelDetailManage> list(ScoringModelDetailManageParam scoringModelDetailManageParam);

    /**
     * 添加评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    void add(ScoringModelDetailManageParam scoringModelDetailManageParam);

    /**
     * 删除评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    void delete(ScoringModelDetailManageParam scoringModelDetailManageParam);

    /**
     * 编辑评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    void edit(ScoringModelDetailManageParam scoringModelDetailManageParam);

    /**
     * 查看评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
     ScoringModelDetailManage detail(ScoringModelDetailManageParam scoringModelDetailManageParam);

    /**
     * 获取有效的评分模型
     * @param timeStart
     * @return
     */
    String getModel(String timeStart);

    /**
     * 根据topId获取分数
     * @param scoringModelId 模型id
     * @param extend01
     * @return
     */
     int getPoint(String scoringModelId, String extend01);
}
