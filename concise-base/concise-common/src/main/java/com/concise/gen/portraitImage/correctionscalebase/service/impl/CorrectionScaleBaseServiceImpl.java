package com.concise.gen.portraitImage.correctionscalebase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.portraitImage.correctionquestion.entity.CorrectionQuestion;
import com.concise.gen.portraitImage.correctionquestion.service.CorrectionQuestionService;
import com.concise.gen.portraitImage.correctionquestionitem.entity.CorrectionQuestionItem;
import com.concise.gen.portraitImage.correctionquestionitem.service.CorrectionQuestionItemService;
import com.concise.gen.portraitImage.correctionscalebase.entity.CorrectionScaleBase;
import com.concise.gen.portraitImage.correctionscalebase.enums.CorrectionScaleBaseExceptionEnum;
import com.concise.gen.portraitImage.correctionscalebase.mapper.CorrectionScaleBaseMapper;
import com.concise.gen.portraitImage.correctionscalebase.param.CorrectionScaleBaseParam;
import com.concise.gen.portraitImage.correctionscalebase.service.CorrectionScaleBaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 量表配置基本信息表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:21
 */
@Service
public class CorrectionScaleBaseServiceImpl extends ServiceImpl<CorrectionScaleBaseMapper, CorrectionScaleBase> implements CorrectionScaleBaseService {

    @Resource
    private CorrectionQuestionService correctionQuestionService;

    @Resource
    private CorrectionQuestionItemService correctionQuestionItemService;

    @Override
    public PageResult<CorrectionScaleBase> page(CorrectionScaleBaseParam correctionScaleBaseParam) {
        QueryWrapper<CorrectionScaleBase> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionScaleBaseParam)) {
            // 根据量表名称 查询
            if (ObjectUtil.isNotEmpty(correctionScaleBaseParam.getTitle())) {
                queryWrapper.lambda().like(CorrectionScaleBase::getTitle, correctionScaleBaseParam.getTitle());
            }
            // 根据启用情况（0：启用 1：停用） 查询
            if (ObjectUtil.isNotEmpty(correctionScaleBaseParam.getStatus())) {
                queryWrapper.lambda().eq(CorrectionScaleBase::getStatus, correctionScaleBaseParam.getStatus());
            }
        }
        queryWrapper.lambda().eq(CorrectionScaleBase::getDelFlag, 0);
        queryWrapper.lambda().orderByAsc(CorrectionScaleBase::getStatus).orderByDesc(CorrectionScaleBase::getUpdateTime);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionScaleBase> list(CorrectionScaleBaseParam correctionScaleBaseParam) {
        return this.list();
    }

    @Override
    @Transactional
    public void add(CorrectionScaleBaseParam correctionScaleBaseParam) {
        //保存量表基本信息
        CorrectionScaleBase correctionScaleBase = new CorrectionScaleBase();
        BeanUtil.copyProperties(correctionScaleBaseParam, correctionScaleBase);
        correctionScaleBase.setCreateTime(new Date());
        correctionScaleBase.setUpdateTime(new Date());
        this.save(correctionScaleBase);
        //保存试题信息
        saveQuestion(correctionScaleBase);
    }

    /**
     * 保存题目
     */
    private void saveQuestion(CorrectionScaleBase correctionScaleBase) {
        for (CorrectionQuestion question : correctionScaleBase.getQuestionList()) {
            //问题
            question.setScaleBaseId(correctionScaleBase.getId());
            question.setId(null);
            correctionQuestionService.save(question);
            //选项
            for (CorrectionQuestionItem questionItem : question.getQuestionItemList()) {
                questionItem.setQuestionId(question.getId());
                questionItem.setId(null);
                correctionQuestionItemService.save(questionItem);
                //关联问题
                if (null == questionItem.getQuestionList()) {
                    continue;
                }
                for (CorrectionQuestion questionGl : questionItem.getQuestionList()) {
                    questionGl.setId(null);
                    questionGl.setRelevanceItemId(questionItem.getId());
                    correctionQuestionService.save(questionGl);
                    //关联问题的选项
                    for (CorrectionQuestionItem questionItemGl : questionGl.getQuestionItemList()) {
                        questionItemGl.setId(null);
                        questionItemGl.setQuestionId(questionGl.getId());
                        correctionQuestionItemService.save(questionItemGl);
                        //关联问题2
                        if (null == questionItemGl.getQuestionList()) {
                            continue;
                        }
                        for (CorrectionQuestion questionGlTwo : questionItemGl.getQuestionList()) {
                            questionGlTwo.setId(null);
                            questionGlTwo.setRelevanceItemId(questionItemGl.getId());
                            correctionQuestionService.save(questionGlTwo);
                            //关联问题的选项2
                            for (CorrectionQuestionItem questionItemGlTwo : questionGlTwo.getQuestionItemList()) {
                                questionItemGlTwo.setId(null);
                                questionItemGlTwo.setQuestionId(questionGlTwo.getId());
                                correctionQuestionItemService.save(questionItemGlTwo);
                            }
                        }
                    }
                }
            }
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionScaleBaseParam correctionScaleBaseParam) {
        this.removeById(correctionScaleBaseParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionScaleBaseParam correctionScaleBaseParam) {
        //修改先把当前记录删除， 并重新生成一条
        CorrectionScaleBase correctionScaleBase = this.queryCorrectionScaleBase(correctionScaleBaseParam);
        BeanUtil.copyProperties(correctionScaleBaseParam, correctionScaleBase);
        correctionScaleBase.setDelFlag(1);
        this.updateById(correctionScaleBase);
        //新增量表
        correctionScaleBase.setId(null);
        correctionScaleBase.setCreateTime(new Date());
        correctionScaleBase.setUpdateTime(new Date());
        correctionScaleBase.setDelFlag(0);
        this.save(correctionScaleBase);
        //新增试题
        saveQuestion(correctionScaleBase);
    }

    @Override
    public CorrectionScaleBase detail(CorrectionScaleBaseParam correctionScaleBaseParam) {
        return this.queryCorrectionScaleBase(correctionScaleBaseParam);
    }

    /**
     * 获取量表配置基本信息表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:21
     */
    private CorrectionScaleBase queryCorrectionScaleBase(CorrectionScaleBaseParam correctionScaleBaseParam) {
        CorrectionScaleBase correctionScaleBase = this.getById(correctionScaleBaseParam.getId());
        if (ObjectUtil.isNull(correctionScaleBase)) {
            throw new ServiceException(CorrectionScaleBaseExceptionEnum.NOT_EXIST);
        }
        //拼试题
        QueryWrapper<CorrectionQuestion> queryQuestion = new QueryWrapper<>();
        queryQuestion.lambda().eq(CorrectionQuestion::getScaleBaseId, correctionScaleBase.getId());
        queryQuestion.lambda().eq(CorrectionQuestion::getDelFlag, 0);
        queryQuestion.lambda().orderByAsc(CorrectionQuestion::getOrderIndex);
        List<CorrectionQuestion> questionList = correctionQuestionService.list(queryQuestion);
        QueryWrapper<CorrectionQuestionItem> queryQuestionItem = null;
        List<CorrectionQuestionItem> itemList = null;
        for (CorrectionQuestion question : questionList) {
            queryQuestionItem = new QueryWrapper<>();
            queryQuestionItem.lambda().eq(CorrectionQuestionItem::getQuestionId, question.getId());
            queryQuestionItem.lambda().eq(CorrectionQuestionItem::getDelFlag, 0);
            queryQuestionItem.lambda().orderByAsc(CorrectionQuestionItem::getOrderIndex);
            itemList = correctionQuestionItemService.list(queryQuestionItem);
            for (CorrectionQuestionItem questionItem : itemList) {
                if (1 == questionItem.getHaveHigherQuestion()) {
                    //有关联问题
                    queryQuestion = new QueryWrapper<>();
                    queryQuestion.lambda().eq(CorrectionQuestion::getRelevanceItemId, questionItem.getId());
                    queryQuestion.lambda().orderByAsc(CorrectionQuestion::getOrderIndex);
                    List<CorrectionQuestion> questionListOne = correctionQuestionService.list(queryQuestion);
                    for (CorrectionQuestion questionOne : questionListOne) {
                        QueryWrapper<CorrectionQuestionItem> queryQuestionItemOne = new QueryWrapper<>();
                        queryQuestionItemOne.lambda().eq(CorrectionQuestionItem::getQuestionId, questionOne.getId());
                        queryQuestionItemOne.lambda().orderByAsc(CorrectionQuestionItem::getOrderIndex);
                        List<CorrectionQuestionItem> itemOneList = correctionQuestionItemService.list(queryQuestionItemOne);
                        for (CorrectionQuestionItem itemOne : itemOneList) {
                            if (1 == itemOne.getHaveHigherQuestion()) {
                                //有关联问题
                                queryQuestion = new QueryWrapper<>();
                                queryQuestion.lambda().eq(CorrectionQuestion::getRelevanceItemId, itemOne.getId());
                                queryQuestion.lambda().orderByAsc(CorrectionQuestion::getOrderIndex);
                                List<CorrectionQuestion> questionListTwoList = correctionQuestionService.list(queryQuestion);
                                for (CorrectionQuestion questionTwo : questionListTwoList) {
                                    QueryWrapper<CorrectionQuestionItem> queryQuestionItemTwo = new QueryWrapper<>();
                                    queryQuestionItemTwo.lambda().eq(CorrectionQuestionItem::getQuestionId, questionTwo.getId());
                                    queryQuestionItemTwo.lambda().orderByAsc(CorrectionQuestionItem::getOrderIndex);
                                    questionTwo.setQuestionItemList(correctionQuestionItemService.list(queryQuestionItemTwo));
                                }
                                itemOne.setQuestionList(questionListTwoList);
                            }
                        }
                        questionOne.setQuestionItemList(itemOneList);
                    }
                    questionItem.setQuestionList(questionListOne);
                }
            }
            question.setQuestionItemList(itemList);
        }
        correctionScaleBase.setQuestionList(questionList);
        return correctionScaleBase;
    }
}
