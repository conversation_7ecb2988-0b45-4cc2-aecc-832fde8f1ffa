package com.concise.sys.modular.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.sys.modular.user.entity.SysUserRole;
import org.apache.ibatis.annotations.Param;

/**
 * 系统用户角色mapper接口
 *
 * <AUTHOR>
 * @date 2020/3/13 15:46
 */
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {
    void insertDefaultRole(@Param("idStr") String idStr, @Param("userId") String userId);
}
