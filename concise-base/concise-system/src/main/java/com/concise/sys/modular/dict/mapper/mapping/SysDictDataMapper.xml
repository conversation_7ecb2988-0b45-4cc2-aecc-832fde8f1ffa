<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.sys.modular.dict.mapper.SysDictDataMapper">

    <resultMap id="dictDataResult" type="com.concise.sys.modular.dict.entity.SysDictData">
        <result column="id" property="id" />
        <result column="type_id" property="typeId" />
        <result column="type_name" property="typeName" />
        <result column="value" property="value" />
        <result column="code" property="code" />
        <result column="sort" property="sort" />
        <result column="remark" property="remark" />
    </resultMap>

    <select id="findList" parameterType="String" resultMap="dictDataResult">
        SELECT sdd.*, (SELECT sdt.name FROM sys_dict_type sdt WHERE sdt.id = sdd.type_id) AS type_name
        FROM sys_dict_data sdd WHERE sdd.remark = #{remark}
        <if test="value != null and value != ''">
            and sdd.value like concat('%',#{value},'%')
        </if>
        ORDER BY code desc
    </select>

    <select id="getDictCodesByDictTypeCode" resultType="java.lang.String">
        SELECT
        dict.`code`
        FROM
        sys_dict_data dict
        INNER JOIN sys_dict_type type ON dict.type_id = type.id
        where type.code in
        <foreach collection="array" index="index" item="i" open="(" separator="," close=")">
            #{i}
        </foreach>
    </select>

    <select id="getByCode" parameterType="String" resultMap="dictDataResult">
        SELECT sdd.id, sdd.type_id, sdd.value, (SELECT sdt.name FROM sys_dict_type sdt WHERE sdt.id = sdd.type_id) AS type_name
        FROM sys_dict_data sdd WHERE sdd.code = #{code}
    </select>
    <select id="listByType" resultType="com.concise.sys.modular.dict.entity.SysDictData">
        SELECT
            sys_dict_data.*
        FROM
            sys_dict_data
                LEFT JOIN sys_dict_type ON sys_dict_data.type_id = sys_dict_type.id
        WHERE
            sys_dict_type.`code` = #{typeCode}
    </select>
</mapper>
