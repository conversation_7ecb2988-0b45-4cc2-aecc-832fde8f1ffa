package com.concise.modular.liteflowscript.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.liteflowscript.entity.LiteflowScript;
import com.concise.modular.liteflowscript.param.LiteflowScriptParam;

/**
 * 规则引擎脚本service接口
 *
 * <AUTHOR>
 * @date 2025-05-08 16:44:31
 */
public interface LiteflowScriptService extends IService<LiteflowScript> {

    /**
     * 查询规则引擎脚本
     *
     * <AUTHOR>
     * @date 2025-05-08 16:44:31
     */
    PageResult<LiteflowScript> page(LiteflowScriptParam liteflowScriptParam);

    /**
     * 规则引擎脚本列表
     *
     * <AUTHOR>
     * @date 2025-05-08 16:44:31
     */
    List<LiteflowScript> list(LiteflowScriptParam liteflowScriptParam);

    /**
     * 添加规则引擎脚本
     *
     * <AUTHOR>
     * @date 2025-05-08 16:44:31
     */
    void add(LiteflowScriptParam liteflowScriptParam);

    /**
     * 删除规则引擎脚本
     *
     * <AUTHOR>
     * @date 2025-05-08 16:44:31
     */
    void delete(LiteflowScriptParam liteflowScriptParam);

    /**
     * 编辑规则引擎脚本
     *
     * <AUTHOR>
     * @date 2025-05-08 16:44:31
     */
    void edit(LiteflowScriptParam liteflowScriptParam);

    /**
     * 查看规则引擎脚本
     *
     * <AUTHOR>
     * @date 2025-05-08 16:44:31
     */
     LiteflowScript detail(LiteflowScriptParam liteflowScriptParam);
}
