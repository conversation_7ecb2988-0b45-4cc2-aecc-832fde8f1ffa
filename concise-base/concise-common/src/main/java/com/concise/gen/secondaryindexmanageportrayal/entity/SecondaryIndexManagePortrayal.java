package com.concise.gen.secondaryindexmanageportrayal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;

import java.util.Date;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 二级指标管理信息表
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:21
 */
@Data
@TableName("secondary_index_manage_portrayal")
public class SecondaryIndexManagePortrayal {

    /**
     * 二级指标ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 上级指标ID
     */
    private String topindexid;

    /**
     * 指标名称
     */
    private String indexName;

    /**
     * 排序
     */
    private String sort;

    /**
     * 指标分值
     */
    private int indexPoint;


    /**
     * 删除状态
     */
    private String delFlag;

    /**
     *
     */
    private String extend01;

    /**
     *
     */
    private String extend02;

    /**
     *
     */
    private String extend03;

}
