package com.concise.sys.modular.dict.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.sys.modular.dict.entity.DictModelMany;
import com.concise.sys.modular.dict.entity.SysDictType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统字典类型mapper接口
 *
 * <AUTHOR>
 * @date 2020/3/13 16:11
 */
public interface SysDictTypeMapper extends BaseMapper<SysDictType> {
    List<DictModelMany> queryManyDictByKeys(@Param("dictCodeList") List<String> dictCodeList, @Param("keys")List<String> keys);

}
