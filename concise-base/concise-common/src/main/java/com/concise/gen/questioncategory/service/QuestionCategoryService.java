package com.concise.gen.questioncategory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.questioncategory.entity.CateTree;
import com.concise.gen.questioncategory.entity.QuestionCategory;
import com.concise.gen.questioncategory.param.QuestionCategoryParam;

import java.util.List;

/**
 * 题库管理service接口
 *
 * <AUTHOR>
 * @date 2023-01-03 16:40:11
 */
public interface QuestionCategoryService extends IService<QuestionCategory> {

    /**
     * 查询题库管理
     *
     * <AUTHOR>
     * @date 2023-01-03 16:40:11
     */
    PageResult<QuestionCategory> page(QuestionCategoryParam questionCategoryParam);

    /**
     * 题库管理列表
     *
     * <AUTHOR>
     * @date 2023-01-03 16:40:11
     */
    List<QuestionCategory> list(QuestionCategoryParam questionCategoryParam);

    /**
     * 添加题库管理
     *
     * <AUTHOR>
     * @date 2023-01-03 16:40:11
     */
    void add(QuestionCategoryParam questionCategoryParam);

    /**
     * 删除题库管理
     *
     * <AUTHOR>
     * @date 2023-01-03 16:40:11
     */
    void delete(QuestionCategoryParam questionCategoryParam);

    /**
     * 编辑题库管理
     *
     * <AUTHOR>
     * @date 2023-01-03 16:40:11
     */
    void edit(QuestionCategoryParam questionCategoryParam);

    /**
     * 查看题库管理
     *
     * <AUTHOR>
     * @date 2023-01-03 16:40:11
     */
    QuestionCategory detail(QuestionCategoryParam questionCategoryParam);

    /**
     * 获取题库分类树
     *
     * @return
     */
    List<CateTree> getCateTree();
}
