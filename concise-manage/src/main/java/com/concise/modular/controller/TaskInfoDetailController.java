package com.concise.modular.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.gen.taskinfo.param.TaskInfoDetailParam;
import com.concise.gen.taskinfo.service.TaskInfoDetailService;
import com.concise.sys.modular.org.service.SysOrgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;

/**
 * 任务详情表控制器
 *
 * <AUTHOR>
 * @date 2022-02-28 15:17:32
 */
@Api(tags = "任务详情表")
@RestController
public class TaskInfoDetailController {

    @Resource
    private TaskInfoDetailService taskInfoDetailService;

    @Resource
    private SysOrgService sysOrgService;
    /**
     * 查询任务详情表
     *
     * <AUTHOR>
     * @date 2022-02-28 15:17:32
     */
    @Permission
    @GetMapping("/taskInfoDetail/page")
    @ApiOperation("任务详情表_分页查询")
    @BusinessLog(title = "任务详情表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(TaskInfoDetailParam taskInfoDetailParam) {
        //若前台没传机构id， 则默认 当前登录人所在机构ID
        String orgId = LoginContextHolder.me().getSysLoginUserOrgId();
        if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getJzjgId())) {
            orgId = taskInfoDetailParam.getJzjgId();
        }
        return new SuccessResponseData(taskInfoDetailService.page(taskInfoDetailParam, sysOrgService.getDeptIds(orgId)));
    }

    /**
     * 添加任务详情表
     *
     * <AUTHOR>
     * @date 2022-02-28 15:17:32
     */
    @Permission
    @PostMapping("/taskInfoDetail/add")
    @ApiOperation("任务详情表_增加")
    @BusinessLog(title = "任务详情表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(TaskInfoDetailParam.add.class) TaskInfoDetailParam taskInfoDetailParam) {
        taskInfoDetailService.add(taskInfoDetailParam);
        return new SuccessResponseData();
    }

    /**
     * 删除任务详情表
     *
     * <AUTHOR>
     * @date 2022-02-28 15:17:32
     */
    @Permission
    @PostMapping("/taskInfoDetail/delete")
    @ApiOperation("任务详情表_删除")
    @BusinessLog(title = "任务详情表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(TaskInfoDetailParam.delete.class) TaskInfoDetailParam taskInfoDetailParam) {
        taskInfoDetailService.delete(taskInfoDetailParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑任务详情表
     *
     * <AUTHOR>
     * @date 2022-02-28 15:17:32
     */
    @Permission
    @PostMapping("/taskInfoDetail/edit")
    @ApiOperation("任务详情表_编辑")
    @BusinessLog(title = "任务详情表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(TaskInfoDetailParam.edit.class) TaskInfoDetailParam taskInfoDetailParam) {
        taskInfoDetailService.edit(taskInfoDetailParam);
        return new SuccessResponseData();
    }

    /**
     * 查看任务详情表
     *
     * <AUTHOR>
     * @date 2022-02-28 15:17:32
     */
    @Permission
    @GetMapping("/taskInfoDetail/detail")
    @ApiOperation("任务详情表_查看")
    @BusinessLog(title = "任务详情表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(TaskInfoDetailParam.detail.class) TaskInfoDetailParam taskInfoDetailParam) {
        return new SuccessResponseData(taskInfoDetailService.detail(taskInfoDetailParam));
    }

    /**
     * 任务详情表列表
     *
     * <AUTHOR>
     * @date 2022-02-28 15:17:32
     */
    @Permission
    @GetMapping("/taskInfoDetail/list")
    @ApiOperation("任务详情表_列表")
    @BusinessLog(title = "任务详情表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(TaskInfoDetailParam taskInfoDetailParam) {
        return new SuccessResponseData(taskInfoDetailService.list(taskInfoDetailParam));
    }

    @Permission
    @GetMapping("/taskInfoDetail/export")
    @BusinessLog(title = "任务详情表_导出", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void export(TaskInfoDetailParam taskInfoDetailParam) {
        //若前台没传机构id， 则默认 当前登录人所在机构ID
        String orgId = LoginContextHolder.me().getSysLoginUserOrgId();
        if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getJzjgId())) {
            orgId = taskInfoDetailParam.getJzjgId();
        }
        taskInfoDetailService.export(taskInfoDetailParam, sysOrgService.getDeptIds(orgId));
    }


    @Permission
    @GetMapping("/taskInfoDetail/count")
    @ApiOperation("任务详情表_统计")
    @BusinessLog(title = "任务详情表_统计", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData taskCount(@Validated(TaskInfoDetailParam.list.class) TaskInfoDetailParam taskInfoDetailParam) {
        return new SuccessResponseData(taskInfoDetailService.taskCount(taskInfoDetailParam));
    }

}
