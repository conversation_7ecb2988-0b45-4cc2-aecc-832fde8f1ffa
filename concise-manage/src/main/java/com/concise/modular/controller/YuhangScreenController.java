package com.concise.modular.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.correctionobjectinformation.entity.ScreenModel;
import com.concise.modular.service.YuhangScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/25
 */
@Api(tags = "余杭精准大屏")
@RestController
public class YuhangScreenController {

    @Resource
    private YuhangScreenService yuhangScreenService;

    @GetMapping("/yhScreen/moodCode")
    @ApiOperation("心理画像_心情码")
    @BusinessLog(title = "心理画像_心情码", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData moodCode() {
        return new SuccessResponseData(yuhangScreenService.moodCode());
    }

    @GetMapping("/yhScreen/law")
    @ApiOperation("知法画像")
    @BusinessLog(title = "知法画像", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData law() {
        List<ScreenModel> law = yuhangScreenService.law();
        //累犯
        law.add(yuhangScreenService.lfgf());
        return new SuccessResponseData(law);
    }

    @GetMapping("/yhScreen/workingCondition")
    @ApiOperation("就业画像_工作状态")
    @BusinessLog(title = "就业画像_工作状态", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData workingCondition() {
        return new SuccessResponseData(yuhangScreenService.workingCondition());
    }

    @GetMapping("/yhScreen/workingType")
    @ApiOperation("就业画像_工作类型")
    @BusinessLog(title = "就业画像_工作类型", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData workingType() {
        return new SuccessResponseData(yuhangScreenService.workingType());
    }

    @GetMapping("/yhScreen/identityAndOccupation")
    @ApiOperation("就业画像_身份职业")
    @BusinessLog(title = "就业画像_身份职业", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData identityAndOccupation() {
        return new SuccessResponseData(yuhangScreenService.identityAndOccupation());
    }

    @GetMapping("/yhScreen/sourceOfIncome")
    @ApiOperation("就业画像_收入来源")
    @BusinessLog(title = "就业画像_收入来源", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData sourceOfIncome() {
        return new SuccessResponseData(yuhangScreenService.sourceOfIncome());
    }

    @GetMapping("/yhScreen/householdIncome")
    @ApiOperation("家庭画像_家庭收入")
    @BusinessLog(title = "家庭画像_家庭收入", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData householdIncome() {
        return new SuccessResponseData(yuhangScreenService.householdIncome());
    }

    @GetMapping("/yhScreen/householdDebt")
    @ApiOperation("家庭画像_家庭负债")
    @BusinessLog(title = "家庭画像_家庭负债", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData householdDebt() {
        return new SuccessResponseData(yuhangScreenService.householdDebt());
    }

    @GetMapping("/yhScreen/familyConflicts")
    @ApiOperation("家庭画像_家庭矛盾")
    @BusinessLog(title = "家庭画像_家庭矛盾", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData familyConflicts() {
        return new SuccessResponseData(yuhangScreenService.familyConflicts());
    }

    @GetMapping("/yhScreen/familyStatus")
    @ApiOperation("家庭画像_家庭状况")
    @BusinessLog(title = "家庭画像_家庭状况", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData familyStatus() {
        return new SuccessResponseData(yuhangScreenService.familyStatus());
    }

    @GetMapping("/yhScreen/familyEconomicStatus")
    @ApiOperation("家庭画像_家庭经济状况")
    @BusinessLog(title = "家庭画像_家庭经济状况", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData familyEconomicStatus() {
        return new SuccessResponseData(yuhangScreenService.familyEconomicStatus());
    }

    @GetMapping("/yhScreen/riskLevel")
    @ApiOperation("风险画像_风险等级")
    @BusinessLog(title = "风险画像_风险等级", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData riskLevel() {
        return new SuccessResponseData(yuhangScreenService.riskLevel());
    }

    @GetMapping("/yhScreen/riskLevelDetail")
    @ApiOperation("风险画像_风险等级_详情")
    @BusinessLog(title = "风险画像_风险等级_详情", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData riskLevelDetail(@RequestParam String title,@RequestParam(required = false) String name) {
        return new SuccessResponseData(yuhangScreenService.riskLevelDetail(title,name));
    }
}
