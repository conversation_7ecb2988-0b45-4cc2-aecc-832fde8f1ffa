package com.concise.sys.core.aop;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;

import cn.hutool.log.Log;
import com.alibaba.fastjson.JSON;
import com.concise.common.annotion.PreventDuplicateSubmission;
import com.concise.common.consts.AopSortConstant;
import com.concise.common.exception.ServiceException;
import com.concise.common.exception.enums.DuplicateSubmissionExceptionEnum;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.core.login.SysLoginUser;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * 防重复提交AOP切面
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@Aspect
@Order(AopSortConstant.PREVENT_DUPLICATE_SUBMISSION_AOP)
public class PreventDuplicateSubmissionAop {

    private static final Log log = Log.get();

    /**
     * Redis key前缀
     */
    private static final String REDIS_KEY_PREFIX = "prevent_duplicate_submission:";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 防重复提交切入点
     */
    @Pointcut("@annotation(com.concise.common.annotion.PreventDuplicateSubmission)")
    private void preventDuplicateSubmissionPointCut() {
    }

    /**
     * 环绕通知，处理防重复提交逻辑
     */
    @Around("preventDuplicateSubmissionPointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        PreventDuplicateSubmission annotation = method.getAnnotation(PreventDuplicateSubmission.class);

        // 生成Redis key
        String redisKey = generateRedisKey(joinPoint, annotation);

        try {
            // 尝试设置Redis key，如果key已存在则返回false
            Boolean success = stringRedisTemplate.opsForValue().setIfAbsent(
                    redisKey, 
                    "1", 
                    annotation.interval(), 
                    annotation.timeUnit()
            );

            if (success == null || !success) {
                // key已存在，说明重复提交
                String message = StrUtil.isNotBlank(annotation.message()) ? 
                    annotation.message() : DuplicateSubmissionExceptionEnum.DUPLICATE_SUBMISSION.getMessage();
                log.warn("检测到重复提交，key: {}", redisKey);
                throw new ServiceException(DuplicateSubmissionExceptionEnum.DUPLICATE_SUBMISSION.getCode(), message);
            }

            // 执行目标方法
            return joinPoint.proceed();

        } catch (ServiceException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            // 删除Redis key，避免因为异常导致key一直存在
            try {
                stringRedisTemplate.delete(redisKey);
            } catch (Exception deleteException) {
                log.error("删除Redis key失败: {}", redisKey, deleteException);
            }
            throw e;
        }
    }

    /**
     * 生成Redis key
     */
    private String generateRedisKey(ProceedingJoinPoint joinPoint, PreventDuplicateSubmission annotation) {
        StringBuilder keyBuilder = new StringBuilder();
        
        // 添加前缀
        keyBuilder.append(REDIS_KEY_PREFIX);
        
        // 添加自定义前缀
        if (StrUtil.isNotBlank(annotation.keyPrefix())) {
            keyBuilder.append(annotation.keyPrefix()).append(":");
        }

        // 获取当前用户ID
        SysLoginUser loginUser = LoginContextHolder.me().getSysLoginUserWithoutException();
        String userId = "anonymous";
        if (ObjectUtil.isNotNull(loginUser)) {
            userId = loginUser.getId();
        }
        keyBuilder.append("user:").append(userId).append(":");

        // 添加方法签名
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String className = methodSignature.getDeclaringTypeName();
        String methodName = methodSignature.getName();
        keyBuilder.append("method:").append(className).append(".").append(methodName);

        // 是否包含参数
        if (annotation.includeParams()) {
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                String argsJson = JSON.toJSONString(args);
                String argsHash = MD5.create().digestHex(argsJson);
                keyBuilder.append(":params:").append(argsHash);
            }
        }

        return keyBuilder.toString();
    }
}
