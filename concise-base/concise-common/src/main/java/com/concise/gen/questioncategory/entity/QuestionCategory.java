package com.concise.gen.questioncategory.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;

import java.util.Date;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 题库管理
 *
 * <AUTHOR>
 * @date 2023-01-03 16:40:11
 */
@Data
@TableName("question_category")
public class QuestionCategory {

    /**
     * 唯一标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 父分类id
     */
    private String parentId;

    /**
     * 分类名
     */
    private String typeName;

    /**
     * 部门id
     */
    private String depId;

    /**
     * 排序
     */
    private Integer indexId;

    /**
     * 分类等级
     */
    private String rank;

    /**
     * 是否删除
     */
    private String isDel;

    /**
     *
     */
    private String createBy;

    /**
     *
     */
    private String updateBy;

    private Date createTime;

    private Date updateTime;

}
