package com.concise.sys.modular.dict.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class DictVo {
    /**
     * 类型名称
     */
    @Excel(name = "类型名称")
    private String typeName;
    /**
     * 类型code
     */
    @Excel(name = "类型code")
    private String typeCode;
    /**
     * 类型排序
     */
    @Excel(name = "类型排序")
    private Integer typeSort;

    /**
     * 唯一编码
     */
    @Excel(name = "唯一编码")
    private String uniqueCode;
    /**
     * 字典值
     */
    @Excel(name = "字典值")
    private String dictValue;
    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer sort;
}
