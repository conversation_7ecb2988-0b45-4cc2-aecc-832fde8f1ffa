package com.concise.gen.secondaryindexmanageportrayal.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.secondaryindexmanage. param.SecondaryIndexManageParam;
import com.concise.gen.secondaryindexmanage. service.SecondaryIndexManageService;
import com.concise.gen.secondaryindexmanageportrayal.param.SecondaryIndexManagePortrayalParam;
import com.concise.gen.secondaryindexmanageportrayal.service.SecondaryIndexManagePortrayalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 二级指标管理信息表控制器
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:21
 */
@Api(tags = "二级指标管理信息表(精准画像)")
@RestController
public class SecondaryIndexManagePortrayalController {

    @Resource
    private SecondaryIndexManagePortrayalService secondaryIndexManagePortrayalService;

    /**
     * 查询二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    @Permission
    @GetMapping("/secondaryIndexManagePortrayal/page")
    @ApiOperation("二级指标管理信息表_分页查询")
    @BusinessLog(title = "二级指标管理信息表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        secondaryIndexManagePortrayalParam.setDelFlag("0");
        return new SuccessResponseData(secondaryIndexManagePortrayalService.page(secondaryIndexManagePortrayalParam));
    }

    /**
     * 添加二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    @Permission
    @PostMapping("/secondaryIndexManagePortrayal/add")
    @ApiOperation("二级指标管理信息表_增加")
    @BusinessLog(title = "二级指标管理信息表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(SecondaryIndexManagePortrayalParam.add.class) SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        secondaryIndexManagePortrayalParam.setDelFlag("0");
        secondaryIndexManagePortrayalService.add(secondaryIndexManagePortrayalParam);
        return new SuccessResponseData();
    }

    /**
     * 删除二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    @Permission
    @PostMapping("/secondaryIndexManagePortrayal/delete")
    @ApiOperation("二级指标管理信息表_删除")
    @BusinessLog(title = "二级指标管理信息表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(SecondaryIndexManagePortrayalParam.delete.class) SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        secondaryIndexManagePortrayalService.delete(secondaryIndexManagePortrayalParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    @Permission
    @PostMapping("/secondaryIndexManagePortrayal/edit")
    @ApiOperation("二级指标管理信息表_编辑")
    @BusinessLog(title = "二级指标管理信息表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(SecondaryIndexManagePortrayalParam.edit.class) SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        secondaryIndexManagePortrayalService.edit(secondaryIndexManagePortrayalParam);
        return new SuccessResponseData();
    }

    /**
     * 查看二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    @Permission
    @GetMapping("/secondaryIndexManagePortrayal/detail")
    @ApiOperation("二级指标管理信息表_查看")
    @BusinessLog(title = "二级指标管理信息表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(SecondaryIndexManagePortrayalParam.detail.class) SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        return new SuccessResponseData(secondaryIndexManagePortrayalService.detail(secondaryIndexManagePortrayalParam));
    }

    /**
     * 二级指标管理信息表列表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    @Permission
    @GetMapping("/secondaryIndexManagePortrayal/list")
    @ApiOperation("二级指标管理信息表_列表")
    @BusinessLog(title = "二级指标管理信息表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        secondaryIndexManagePortrayalParam.setDelFlag("0");
        return new SuccessResponseData(secondaryIndexManagePortrayalService.list(secondaryIndexManagePortrayalParam));
    }

    /**
     * 二级指标管理信息表列表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    @Permission
    @GetMapping("/secondaryIndexManagePortrayal/tree")
    @ApiOperation("二级指标管理信息表_树")
    @BusinessLog(title = "二级指标管理信息表_树", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData tree(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        secondaryIndexManagePortrayalParam.setDelFlag("0");
        return new SuccessResponseData(secondaryIndexManagePortrayalService.tree(secondaryIndexManagePortrayalParam));
    }
}
