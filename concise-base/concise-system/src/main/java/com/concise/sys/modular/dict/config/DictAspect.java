package com.concise.sys.modular.dict.config;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.pojo.response.ResponseData;
import com.concise.sys.modular.dict.entity.DictModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 字典aop类
 * @Author: dangzhenghui
 * @Date: 2019-3-17 21:50
 * @Version: 1.0
 */
@Aspect
@Component
@Slf4j
public class DictAspect {

    @Autowired
    private DictAPI commonAPI;
    @Autowired
    public StringRedisTemplate redisTemplate;

    // 定义切点Pointcut
    @Pointcut("execution(public * com.concise..*.*Controller.*(..))")
    public void excudeService() {
    }

    @Around("excudeService()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        long time1 = System.currentTimeMillis();
        Object result = pjp.proceed();
        long time2 = System.currentTimeMillis();
        log.debug("获取JSON数据 耗时：" + (time2 - time1) + "ms");
        long start = System.currentTimeMillis();
        this.parseDictText(result);
        long end = System.currentTimeMillis();
        log.debug("注入字典到JSON数据  耗时" + (end - start) + "ms");
        return result;
    }

    /**
     * 本方法针对返回对象为Result 的IPage的分页列表数据进行动态字典注入
     * 字典注入实现 通过对实体类添加注解@dict 来标识需要的字典内容,字典分为单字典code即可 ，table字典 code table text配合使用与原来jeecg的用法相同
     * 示例为SysUser   字段为sex 添加了注解@Dict(dicCode = "sex") 会在字典服务立马查出来对应的text 然后在请求list的时候将这个字典text，已字段名称加_dictText形式返回到前端
     * 例输入当前返回值的就会多出一个sex_dictText字段
     * {
     * sex:1,
     * sex_dictText:"男"
     * }
     * 前端直接取值sext_dictText在table里面无需再进行前端的字典转换了
     * customRender:function (text) {
     * if(text==1){
     * return "男";
     * }else if(text==2){
     * return "女";
     * }else{
     * return text;
     * }
     * }
     * 目前vue是这么进行字典渲染到table上的多了就很麻烦了 这个直接在服务端渲染完成前端可以直接用
     *
     * @param result
     */
    private void parseDictText(Object result) {
        if (result instanceof ResponseData) {
            if (((ResponseData) result).getData() instanceof PageResult) {
                List<JSONObject> items = new ArrayList<>();

                //step.1 筛选出加了 Dict 注解的字段列表
                List<Field> dictFieldList = new ArrayList<>();
                // 字典数据列表， key = 字典code，value=数据列表
                Map<String, List<String>> dataListMap = new HashMap<>();
                if (CollectionUtil.isNotEmpty(((PageResult) ((ResponseData) result).getData()).getRows())) {
                    for (Object record : ((PageResult) ((ResponseData) result).getData()).getRows()) {
                        ObjectMapper mapper = new ObjectMapper();
                        String json = "{}";
                        try {
                            //解决@JsonFormat注解解析不了的问题详见SysAnnouncement类的@JsonFormat
                            json = mapper.writeValueAsString(record);
                        } catch (JsonProcessingException e) {
                            log.error("json解析失败" + e.getMessage(), e);
                        }
                        JSONObject item = JSONObject.parseObject(json);
                        //update-begin--Author:scott -- Date:20190603 ----for：解决继承实体字段无法翻译问题------
                        //for (Field field : record.getClass().getDeclaredFields()) {
                        // 遍历所有字段，把字典Code取出来，放到 map 里
                        for (Field field : oConvertUtils.getAllFields(record)) {
                            String value = item.getString(field.getName());
                            if (oConvertUtils.isEmpty(value)) {
                                continue;
                            }
                            //update-end--Author:scott  -- Date:20190603 ----for：解决继承实体字段无法翻译问题------
                            if (field.getAnnotation(Dict.class) != null) {
                                if (!dictFieldList.contains(field)) {
                                    dictFieldList.add(field);
                                }
                                String code = field.getAnnotation(Dict.class).dicCode();
                                String text = field.getAnnotation(Dict.class).dicText();
                                String table = field.getAnnotation(Dict.class).dictTable();

                                List<String> dataList;
                                String dictCode = code;
                                if (!StringUtils.isEmpty(table)) {
                                    dictCode = String.format("%s,%s,%s", table, text, code);
                                }
                                dataList = dataListMap.computeIfAbsent(dictCode, k -> new ArrayList<>());
                                this.listAddAllDeduplicate(dataList, Arrays.asList(value.split(",")));
                            }
                            //date类型默认转换string格式化日期
                            if (field.getType().getName().equals("java.util.Date") && field.getAnnotation(JsonFormat.class) == null && item.get(field.getName()) != null) {
                                SimpleDateFormat aDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                Object timeValue = item.get(field.getName());
                                if (timeValue instanceof Integer) {
                                    // 处理 Integer 类型
                                    item.put(field.getName(), aDate.format(new Date(((Integer) timeValue).longValue())));
                                } else if (timeValue instanceof Long) {
                                    // 处理 Long 类型
                                    item.put(field.getName(), aDate.format(new Date((Long) timeValue)));
                                }
                            }
                        }
                        items.add(item);
                    }
                }
                long start = System.currentTimeMillis();
                log.debug("开始翻译所有的字典，时间：" + start);
                //step.2 调用翻译方法，一次性翻译
                Map<String, List<DictModel>> translText = this.translateAllDict(dataListMap);
                long end = System.currentTimeMillis();
                log.debug("翻译所有的字典，时间：" + end + "翻译耗时：" + (end - start));
                log.debug("开始将翻译结果填充到返回结果里，时间：" + System.currentTimeMillis());
                //step.3 将翻译结果填充到返回结果里
                for (JSONObject record : items) {
                    for (Field field : dictFieldList) {
                        String code = field.getAnnotation(Dict.class).dicCode();
                        String text = field.getAnnotation(Dict.class).dicText();
                        String table = field.getAnnotation(Dict.class).dictTable();

                        String fieldDictCode = code;
                        if (!StringUtils.isEmpty(table)) {
                            fieldDictCode = String.format("%s,%s,%s", table, text, code);
                        }

                        String value = record.getString(field.getName());
                        if (oConvertUtils.isNotEmpty(value)) {
                            List<DictModel> dictModels = translText.get(fieldDictCode);
                            if (dictModels == null || dictModels.size() == 0) {
                                continue;
                            }

                            String textValue = this.translDictText(dictModels, value);

                            record.put(field.getName() + "_dictText", textValue);
                        }
                    }
                }

                ((PageResult) ((ResponseData) result).getData()).setRows(items);
            }

        }
    }

    /**
     * list 去重添加
     */
    private void listAddAllDeduplicate(List<String> dataList, List<String> addList) {
        // 筛选出dataList中没有的数据
        List<String> filterList = addList.stream().filter(i -> !dataList.contains(i)).collect(Collectors.toList());
        dataList.addAll(filterList);
    }

    /**
     * 一次性把所有的字典都翻译了
     * 1.  所有的普通数据字典的所有数据只执行一次SQL
     * 2.  表字典相同的所有数据只执行一次SQL
     *
     * @param dataListMap
     * @return
     */
    private Map<String, List<DictModel>> translateAllDict(Map<String, List<String>> dataListMap) {
        Map<String, List<DictModel>> translText = new HashMap<>();
        List<String> needTranslData = new ArrayList<>();
        List<String> needTranslDataTable = new ArrayList<>();

        // Step 1: 先通过redis中获取缓存字典数据
        for (String dictCode : dataListMap.keySet()) {
            List<String> dataList = dataListMap.get(dictCode);
            if (dataList.isEmpty()) continue;

            for (String s : dataList) {
                String data = s.trim();
                if (data.isEmpty()) continue;

                if (dictCode.contains(",")) {
                    // 表字典
                    String keyString = String.format("sys:cache:dictTable::SimpleKey [%s,%s]", dictCode, data);
                    if (redisTemplate.hasKey(keyString)) {
                        String text = redisTemplate.opsForValue().get(keyString);
                        translText.computeIfAbsent(dictCode, k -> new ArrayList<>()).add(new DictModel(data, text));
                    } else {
                        needTranslDataTable.add(data);
                    }
                } else {
                    // 普通字典
                    long start = System.currentTimeMillis();
                    log.debug("普通字典开始翻译，时间：" + start);
                    String keyString = String.format("sys:cache:dict::%s:%s", dictCode, data);
                    if (redisTemplate.hasKey(keyString)) {
                        String text = redisTemplate.opsForValue().get(keyString);
                        translText.computeIfAbsent(dictCode, k -> new ArrayList<>()).add(new DictModel(data, text));
                    } else {
                        needTranslData.add(data);
                    }
                    long end = System.currentTimeMillis();
                    log.debug("普通字典翻译结束，时间：" + end + "耗时：" + (end - start));
                }
            }
        }

        // Step 2: 批量翻译表字典
        if (!needTranslDataTable.isEmpty()) {
            batchTranslateTableDict(needTranslDataTable, translText);
        }

        // Step 3: 批量翻译普通字典
        if (!needTranslData.isEmpty()) {
            batchTranslateNormalDict(needTranslData, translText, dataListMap);
        }

        return translText;
    }

    private void batchTranslateTableDict(List<String> needTranslDataTable, Map<String, List<DictModel>> translText) {
        // 批量翻译表字典的逻辑
        // 这里可以根据需要实现具体的批量查询逻辑
    }

    private void batchTranslateNormalDict(List<String> needTranslData, Map<String, List<DictModel>> translText, Map<String, List<String>> dataListMap) {
        // 批量翻译普通字典的逻辑
        // 这里可以根据需要实现具体的批量查询逻辑
    }

    /**
     * 字典值替换文本
     *
     * @param dictModels
     * @param values
     * @return
     */
    private String translDictText(List<DictModel> dictModels, String values) {
        List<String> result = new ArrayList<>();

        // 允许多个逗号分隔，允许传数组对象
        String[] splitVal = values.split(",");
        for (String val : splitVal) {
            String dictText = val;
            for (DictModel dict : dictModels) {
                if (val.equals(dict.getValue())) {
                    dictText = dict.getText();
                    break;
                }
            }
            result.add(dictText);
        }
        return String.join(",", result);
    }


}
