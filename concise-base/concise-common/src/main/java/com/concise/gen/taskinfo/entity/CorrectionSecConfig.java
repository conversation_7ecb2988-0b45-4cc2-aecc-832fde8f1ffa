package com.concise.gen.taskinfo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 安保时段
 *
 * <AUTHOR>
 * @date 2022-08-04 17:56:42
 */
@Data
public class CorrectionSecConfig {

    /**
     *
     */
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 矫正机构
     */
    private String jzjg;

    /**
     * 矫正机构名称
     */
    private String jzjgName;

    /**
     * 安保类型
     */
    private String secType;

    private String remarks;

    /**
     * 安保开始时间
     */
    private Date startTime;

    /**
     * 安保结束时间
     */
    private Date endTime;

    private String time;

    /**
     * 状态（字典 0草稿 1发布 2撤回 3删除）
     */
    private Integer status;

    /**
     * 安保类型
     */
    @TableField(exist = false)
    private String secTypeName;
}
