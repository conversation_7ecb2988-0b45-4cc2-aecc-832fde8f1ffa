package com.concise.modular.risk.riskevaluatemanage.service.impl;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.page.PageResult;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.modular.risk.enums.OperateTypeEnum;
import com.concise.modular.risk.riskevaluateindexrecord.entity.RiskEvaluateIndexRecord;
import com.concise.modular.risk.riskevaluateindexrecord.service.RiskEvaluateIndexRecordService;
import com.concise.modular.risk.riskevaluatemanage.dto.EvaluationDetailDto;
import com.concise.modular.risk.riskevaluatemanage.entity.RiskEvaluateContext;
import com.concise.modular.risk.riskevaluatemanage.entity.RiskEvaluateManage;
import com.concise.modular.risk.riskevaluatemanage.enums.RiskEvaluateManageExceptionEnum;
import com.concise.modular.risk.riskevaluatemanage.mapper.RiskEvaluateManageMapper;
import com.concise.modular.risk.riskevaluatemanage.param.RiskEvaluateManageParam;
import com.concise.modular.risk.riskevaluatemanage.service.RiskEvaluateManageService;
import com.concise.modular.risk.riskindexmanage.entity.RiskIndexManage;
import com.concise.modular.risk.riskprocessrecord.constants.RiskProcessConstants;
import com.concise.modular.risk.riskprocessrecord.param.RiskProcessRecordParam;
import com.concise.modular.risk.riskprocessrecord.service.RiskProcessRecordService;
import com.concise.modular.risk.risktranscriptmanagement.entity.RiskTranscriptManagement;
import com.concise.modular.risk.risktranscriptmanagement.entity.TranscriptBasicInfo;
import com.concise.modular.risk.risktranscriptmanagement.param.RiskTranscriptManagementParam;
import com.concise.modular.risk.risktranscriptmanagement.service.RiskTranscriptManagementService;
import com.concise.modular.risk.risktranscripttemplate.service.RiskTranscriptTemplateService;
import com.concise.modular.risk.util.RiskIndexExcelExportUtil;
import com.concise.modular.utils.WordUtil;
import com.concise.paper.entity.PaperMaintenance;
import com.concise.paper.entity.PaperTopic;
import com.concise.paper.param.PaperMaintenanceParam;
import com.concise.paper.param.PaperTopicParam;
import com.concise.paper.service.PaperMaintenanceService;
import com.concise.sys.modular.org.service.SysOrgService;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 危险性评估管理service接口实现类
 *
 * <AUTHOR>
 * @date 2025-07-14 15:58:13
 */
@Slf4j
@Service
public class RiskEvaluateManageServiceImpl extends ServiceImpl<RiskEvaluateManageMapper, RiskEvaluateManage> implements RiskEvaluateManageService {

    @Resource
    private SysOrgService sysOrgService;

    @Resource
    private FlowExecutor flowExecutor;

    @Resource
    private RiskTranscriptManagementService riskTranscriptManagementService;

    @Resource
    private CorrectionObjectInformationService correctionObjectInformationService;

    @Resource
    private RiskProcessRecordService riskProcessRecordService;

    @Resource
    private RiskTranscriptTemplateService riskTranscriptTemplateService;

    @Resource
    private SysFileInfoService sysFileInfoService;

    @Resource
    private PaperMaintenanceService paperMaintenanceService;

    @Resource
    private RiskEvaluateIndexRecordService riskEvaluateIndexRecordService;

    //笔录不存在常量
    public static final String TRANSCRIPT_NOT_EXIST = "笔录不存在";


    @Override
    public PageResult<RiskEvaluateManage> page(RiskEvaluateManageParam riskEvaluateManageParam) {
        QueryWrapper<RiskEvaluateManage> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(riskEvaluateManageParam)) {

            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(riskEvaluateManageParam.getXm())) {
                queryWrapper.lambda().like(RiskEvaluateManage::getXm, riskEvaluateManageParam.getXm());
            }
            // 根据矫正单位 查询
            if (ObjectUtil.isNotEmpty(riskEvaluateManageParam.getJzjgName())) {
                queryWrapper.lambda().eq(RiskEvaluateManage::getJzjgName, riskEvaluateManageParam.getJzjgName());
            }
            // 根据截止时间 查询
            if (ObjectUtil.isNotEmpty(riskEvaluateManageParam.getDeadline_begin())) {
                queryWrapper.lambda().ge(RiskEvaluateManage::getDeadline, riskEvaluateManageParam.getDeadline_begin());
            }
            if (ObjectUtil.isNotEmpty(riskEvaluateManageParam.getDeadline_end())) {
                queryWrapper.lambda().le(RiskEvaluateManage::getDeadline, riskEvaluateManageParam.getDeadline_end());
            }
            // 根据进度百分比 查询
            if (ObjectUtil.isNotEmpty(riskEvaluateManageParam.getProgress())) {
                queryWrapper.lambda().eq(RiskEvaluateManage::getProgress, riskEvaluateManageParam.getProgress());
            }
            // 根据状态 查询
            if (ObjectUtil.isNotEmpty(riskEvaluateManageParam.getStatus())) {
                queryWrapper.lambda().eq(RiskEvaluateManage::getStatus, riskEvaluateManageParam.getStatus());
            }
            // 根据得分 查询
            if (ObjectUtil.isNotEmpty(riskEvaluateManageParam.getScore())) {
                queryWrapper.lambda().eq(RiskEvaluateManage::getScore, riskEvaluateManageParam.getScore());
            }
            // 根据结论 查询
            if (ObjectUtil.isNotEmpty(riskEvaluateManageParam.getConclusion())) {
                queryWrapper.lambda().eq(RiskEvaluateManage::getConclusion, riskEvaluateManageParam.getConclusion());
            }
            // 根据创建人姓名 查询
            if (ObjectUtil.isNotEmpty(riskEvaluateManageParam.getCreateUserName())) {
                queryWrapper.lambda().eq(RiskEvaluateManage::getCreateUserName, riskEvaluateManageParam.getCreateUserName());
            }
            // 根据更新人姓名 查询
            if (ObjectUtil.isNotEmpty(riskEvaluateManageParam.getUpdateUserName())) {
                queryWrapper.lambda().eq(RiskEvaluateManage::getUpdateUserName, riskEvaluateManageParam.getUpdateUserName());
            }
            //生成时间
            if (ObjectUtil.isNotEmpty(riskEvaluateManageParam.getCreateTime_begin())) {
                queryWrapper.lambda().ge(RiskEvaluateManage::getCreateTime, riskEvaluateManageParam.getCreateTime_begin());
            }
            if (ObjectUtil.isNotEmpty(riskEvaluateManageParam.getCreateTime_end())) {
                queryWrapper.lambda().le(RiskEvaluateManage::getCreateTime, riskEvaluateManageParam.getCreateTime_end());
            }

        }
        //更新时间倒序
        queryWrapper.lambda().orderByDesc(RiskEvaluateManage::getUpdateTime);
        //只查询未删除的
        queryWrapper.lambda().eq(RiskEvaluateManage::getDelFlag, 0);
        //数据权限范围内的
        if (ObjectUtil.isAllEmpty(LoginContextHolder.me().getSysLoginUserOrgId(), riskEvaluateManageParam.getJzjg())
                && !LoginContextHolder.me().isSuperAdmin()) {
            return new PageResult<>();
        }
        sysOrgService.buildDataScope(queryWrapper, riskEvaluateManageParam.getJzjg(), "jzjg");
        PageResult<RiskEvaluateManage> pageResult = new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
        if (CollUtil.isNotEmpty(pageResult.getRows())) {
            for (RiskEvaluateManage riskEvaluateManage : pageResult.getRows()) {
                getProgress(riskEvaluateManage);
            }
        }
        return pageResult;
    }

    /**
     * 评估进度换算
     *
     * @param riskEvaluateManage
     * @return
     */
    private void getProgress(RiskEvaluateManage riskEvaluateManage) {
        if (ObjectUtil.isEmpty(riskEvaluateManage.getProgress())) {
            return;
        }
        //进度100，绿色，按时完成
        if (riskEvaluateManage.getProgress() == 100) {
            riskEvaluateManage.setProgressColor("green");
            riskEvaluateManage.setProgressText("按时完成");
        }
        //进度0-99
        if (riskEvaluateManage.getProgress() < 100) {
            //没有截止时间就返回
            if (ObjectUtil.isEmpty(riskEvaluateManage.getDeadline())) {
                return;
            }
            //超过截止时间，红色，逾期n天
            if (DateUtil.date().after(riskEvaluateManage.getDeadline())) {
                riskEvaluateManage.setProgressColor("red");
                long day = DateUtil.betweenDay(riskEvaluateManage.getDeadline(), DateUtil.date(), false);
                riskEvaluateManage.setProgressText("逾期" + day + "天");
            }
            //未超过截止时间，黄色，剩余n天
            if (DateUtil.date().before(riskEvaluateManage.getDeadline())) {
                riskEvaluateManage.setProgressColor("orange");
                long day = DateUtil.betweenDay(DateUtil.date(), riskEvaluateManage.getDeadline(), false);
                riskEvaluateManage.setProgressText("剩余" + day + "天");
            }
        }
    }


    @Override
    public List<RiskEvaluateManage> list(RiskEvaluateManageParam riskEvaluateManageParam) {
        return this.list();
    }

    @Override
    public void add(RiskEvaluateManageParam riskEvaluateManageParam) {
        RiskEvaluateManage riskEvaluateManage = new RiskEvaluateManage();
        BeanUtil.copyProperties(riskEvaluateManageParam, riskEvaluateManage);
        riskEvaluateManage.setDelFlag("0");
        riskEvaluateManage.setCreateUserName(LoginContextHolder.me().getSysLoginUser().getName());
        riskEvaluateManage.setUpdateTime(DateUtil.date());
        this.save(riskEvaluateManage);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(RiskEvaluateManageParam riskEvaluateManageParam) {
        //软删除
        RiskEvaluateManage riskEvaluateManage = this.getById(riskEvaluateManageParam.getId());
        if (ObjectUtil.isNull(riskEvaluateManage)) {
            throw new ServiceException(RiskEvaluateManageExceptionEnum.NOT_EXIST);
        }
        riskEvaluateManage.setDelFlag("1");
        this.updateById(riskEvaluateManage);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(RiskEvaluateManageParam riskEvaluateManageParam) {
        RiskEvaluateManage riskEvaluateManage = this.queryRiskEvaluateManage(riskEvaluateManageParam);
        BeanUtil.copyProperties(riskEvaluateManageParam, riskEvaluateManage);
        riskEvaluateManage.setUpdateUserName(LoginContextHolder.me().getSysLoginUser().getName());
        this.updateById(riskEvaluateManage);
    }

    @Override
    public RiskEvaluateContext detail(RiskEvaluateManageParam riskEvaluateManageParam) {
        RiskEvaluateManage riskEvaluateManage = this.getById(riskEvaluateManageParam.getId());
        if (ObjectUtil.isNull(riskEvaluateManage)) {
            throw new ServiceException(RiskEvaluateManageExceptionEnum.NOT_EXIST);
        }
        RiskEvaluateContext riskEvaluateContext = new RiskEvaluateContext();
        flowExecutor.execute2Resp("riskEvaluateManageDetailChain", null, riskEvaluateManage, riskEvaluateContext);
        return riskEvaluateContext;
    }

    /**
     * 获取危险性评估管理
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    private RiskEvaluateManage queryRiskEvaluateManage(RiskEvaluateManageParam riskEvaluateManageParam) {
        RiskEvaluateManage riskEvaluateManage = this.getById(riskEvaluateManageParam.getId());
        if (ObjectUtil.isNull(riskEvaluateManage)) {
            throw new ServiceException(RiskEvaluateManageExceptionEnum.NOT_EXIST);
        }
        RiskEvaluateContext riskEvaluateContext = new RiskEvaluateContext();
        flowExecutor.execute2Resp("riskEvaluateManageDetailChain", null, riskEvaluateManage, riskEvaluateContext);
        return riskEvaluateManage;
    }

    @Override
    public RiskEvaluateManage initEvaluateManage(String id) {
        RiskEvaluateManage riskEvaluateManage = new RiskEvaluateManage();
        riskEvaluateManage.setJzdxId(id);
        flowExecutor.execute2Resp("formCollectionChain", null, riskEvaluateManage);
        return riskEvaluateManage;
    }

    @Override
    public PageResult<RiskTranscriptManagement> getAllTranscript(String jzdxId) {
        QueryWrapper<RiskTranscriptManagement> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RiskTranscriptManagement::getJzdxId, jzdxId);
        queryWrapper.lambda().eq(RiskTranscriptManagement::getDelFlag, 0);
        queryWrapper.lambda().orderByDesc(RiskTranscriptManagement::getTranscriptTime);
        PageResult<RiskTranscriptManagement> pageResult = new PageResult<>(riskTranscriptManagementService.page(PageFactory.defaultPage(), queryWrapper));
        return pageResult;
    }

    @Override
    public void generateEvaluateByPerson(CorrectionObjectInformation correctionObjectInformation, String evaluateType, RiskEvaluateContext riskEvaluateContext) {
        //先判断是否已有入矫评估记录，有就不生成
        int count = this.count(new LambdaQueryWrapper<RiskEvaluateManage>().eq(RiskEvaluateManage::getJzdxId, correctionObjectInformation.getId()).eq(RiskEvaluateManage::getEvaluateType, evaluateType));
        if (count > 0 && "1".equals(evaluateType)) {
            throw new RuntimeException("入矫评估已存在，不能重复生成");
        }

        //生成评估数据
        riskEvaluateContext.setEvaluateType(evaluateType);
        riskEvaluateContext.setCorrectionObjectInformation(correctionObjectInformation);
        RiskEvaluateManage riskEvaluateManage = new RiskEvaluateManage();
        riskEvaluateManage.setId(IdWorker.getIdStr());
        riskEvaluateContext.setRiskEvaluateManage(riskEvaluateManage);
        flowExecutor.execute2Resp("entryChain", null, riskEvaluateContext);
        //初始化流程
        riskProcessRecordService.initProcess(riskEvaluateManage.getId());
    }


    @Override
    public void generateEntryEvaluate() {
        //查询当日入矫数据
        List<CorrectionObjectInformation> list = correctionObjectInformationService.list(new QueryWrapper<CorrectionObjectInformation>().lambda().ge(CorrectionObjectInformation::getRujiaoriqi, DateUtil.beginOfDay(DateUtil.date())));
        if (CollUtil.isNotEmpty(list)) {
            for (CorrectionObjectInformation correctionObjectInformation : list) {
                RiskEvaluateContext riskEvaluateContext = new RiskEvaluateContext();
                generateEvaluateByPerson(correctionObjectInformation, "1", riskEvaluateContext);
                //保存记录
                this.save(riskEvaluateContext.getRiskEvaluateManage());
                //保存笔录
                riskTranscriptManagementService.saveBatch(riskEvaluateContext.getRiskTranscriptManagementList());
            }
        }
    }

    @Override
    public void submitTranscript(RiskTranscriptManagementParam riskTranscriptManagementParam) {
        RiskTranscriptManagement riskTranscriptManagement = riskTranscriptManagementService.getById(riskTranscriptManagementParam.getId());
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("riskTranscriptHandelChain", null, riskTranscriptManagementParam, riskTranscriptManagement);
        if (!liteflowResponse.isSuccess()) {
            throw new ServiceException(500, liteflowResponse.getMessage());
        } else {
            riskTranscriptManagement.setStatus(riskTranscriptManagementParam.getStatus());
            riskTranscriptManagementService.updateById(riskTranscriptManagement);
        }
        //创建并更新流程状态
        riskProcessRecordService.updateProcessProgress(riskTranscriptManagement.getPid(), RiskProcessConstants.ProcessStep.RECORD_COLLECTION_CODE, RiskProcessConstants.ProcessStatus.COMPLETED, null, null);
        //如果是非暂存状态，则生成pdf，并存到oss
        if (ObjectUtil.isNotEmpty(riskTranscriptManagement.getStatus()) && "2".equals(riskTranscriptManagement.getStatus())) {
            try {
                //生成pdf文件
                File pdfFile = generatePdfFile(riskTranscriptManagementParam);
                if (pdfFile != null) {
                    //将PDF文件上传到OSS
                    SysFileInfo sysFileInfo = uploadPdfToOss(pdfFile, riskTranscriptManagement.getTranscriptName());
                    if (sysFileInfo != null) {
                        //更新笔录记录的PDF文件ID
                        riskTranscriptManagement.setPdfId(sysFileInfo.getId().toString());
                        riskTranscriptManagementService.updateById(riskTranscriptManagement);
                        log.info("PDF文件已成功上传到OSS，文件ID：{}", sysFileInfo.getId());
                    }
                    //清理临时文件
                    if (pdfFile.exists()) {
                        boolean delete = pdfFile.delete();
                        if (!delete) {
                            log.warn("删除临时文件失败: {}", pdfFile.getAbsolutePath());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("生成PDF并上传到OSS失败：{}", e.getMessage(), e);
                throw new ServiceException(500, "生成PDF并上传到OSS失败：" + e.getMessage());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitEvaluate(RiskEvaluateContext riskEvaluateContext) {
        // 常量定义
        final int PROGRESS_MANUAL_SCORING = 75;
        final int PROGRESS_COMPLETED = 100;
        final String STATUS_COMPLETED = "1";
        final String MANUAL_SCORE_REQUIRED_MSG = "人工评分不能为空";

        String evaluateId = riskEvaluateContext.getRiskEvaluateManage().getId();
        String operateType = riskEvaluateContext.getOperateType();

        log.info("开始提交风险评估，评估ID: {}, 操作类型: {}", evaluateId, operateType);

        try {
            // 1. 更新进度（执行风险流程链）
            updateRiskProgress(riskEvaluateContext);

            // 2. 处理表单数据提交
            handleFormDataSubmission(riskEvaluateContext);

            // 3. 根据操作类型执行相应的业务逻辑
            boolean needsUpdate = false;
            if (OperateTypeEnum.ONE_ONE.getCode().equals(operateType)) {
                // 信息采集暂存：仅保存信息采集数据
                log.info("执行信息采集暂存，评估ID: {}", evaluateId);
                needsUpdate = true;
            }

            if (OperateTypeEnum.ONE_TWO.getCode().equals(operateType)) {
                // 信息和表单提交并下一步：计算系统评分，更新进度到75%
                log.info("执行信息和表单提交并下一步，评估ID: {}", evaluateId);
                handleInfoAndFormSubmission(riskEvaluateContext, PROGRESS_MANUAL_SCORING);
                needsUpdate = true;
            } else if (OperateTypeEnum.TWO_ONE.getCode().equals(operateType)) {
                // 人工评分暂存：仅保存人工评分数据
                log.info("执行人工评分暂存，评估ID: {}", evaluateId);
                needsUpdate = true;
            } else if (OperateTypeEnum.TWO_TWO.getCode().equals(operateType)) {
                // 人工评分提交并下一步：计算总分和风险等级，完成评估
                log.info("执行人工评分提交并下一步，评估ID: {}", evaluateId);
                handleManualScoreSubmission(riskEvaluateContext, PROGRESS_COMPLETED, STATUS_COMPLETED, MANUAL_SCORE_REQUIRED_MSG);
                needsUpdate = true;
            } else {
                log.warn("未知的操作类型，评估ID: {}, 操作类型: {}", evaluateId, operateType);
            }

            // 4. 统一执行数据库更新操作
            if (needsUpdate) {
                this.updateById(riskEvaluateContext.getRiskEvaluateManage());
                log.info("风险评估数据更新完成，评估ID: {}", evaluateId);
            }

            log.info("风险评估提交成功，评估ID: {}, 操作类型: {}", evaluateId, operateType);

        } catch (Exception e) {
            log.error("提交风险评估失败，评估ID: {}, 操作类型: {}", evaluateId, operateType, e);
            throw e;
        }
    }

    /**
     * 更新风险评估进度
     *
     * @param riskEvaluateContext 风险评估上下文
     */
    private void updateRiskProgress(RiskEvaluateContext riskEvaluateContext) {
        try {
            flowExecutor.execute2Resp("riskProcessChain", null, riskEvaluateContext);
        } catch (Exception e) {
            log.error("执行风险流程链失败，评估ID: {}",
                    riskEvaluateContext.getRiskEvaluateManage().getId(), e);
            throw new ServiceException(500, "更新风险评估进度失败: " + e.getMessage());
        }
    }

    /**
     * 处理表单数据提交
     * 如果表单数据有已经选择的选项，且流程记录里没有信息采集的流程记录，则添加一条
     *
     * @param riskEvaluateContext 风险评估上下文
     */
    private void handleFormDataSubmission(RiskEvaluateContext riskEvaluateContext) {
        // 检查是否有表单数据
        if (ObjectUtil.isEmpty(riskEvaluateContext.getFormPaper()) ||
                CollUtil.isEmpty(riskEvaluateContext.getFormPaper().getTopicList())) {
            log.debug("无表单数据需要处理，评估ID: {}", riskEvaluateContext.getRiskEvaluateManage().getId());
            return;
        }

        String evaluateId = riskEvaluateContext.getRiskEvaluateManage().getId();
        log.info("开始处理表单数据提交，评估ID: {}", evaluateId);

        // 保存表单数据到JSON字段
        riskEvaluateContext.getRiskEvaluateManage()
                .setFormCollectionJsonWrite(JSON.toJSONString(riskEvaluateContext.getFormPaper()));
        log.info("表单数据已保存到JSON字段，评估ID: {}", evaluateId);
        //检测表单有没有选，有的话才保存流程进度
        if (!hasFormContent(riskEvaluateContext.getFormPaper().getTopicList())) {
            log.info("表单无填写内容，评估ID: {}", evaluateId);
            return;
        }
        //保存流程进度
        riskProcessRecordService.updateProcessProgress(riskEvaluateContext.getRiskEvaluateManage().getId(), RiskProcessConstants.ProcessStep.FORM_COLLECTION_CODE, RiskProcessConstants.ProcessStatus.COMPLETED, null, riskEvaluateContext);
    }

    /**
     * 检查表单是否有填写内容
     *
     * @param topicList 表单题目列表
     * @return true-有填写内容，false-无填写内容
     */
    private boolean hasFormContent(List<PaperTopic> topicList) {
        return topicList.stream()
                .anyMatch(topic -> ObjectUtil.isNotEmpty(topic.getUserSelectId()) ||
                        ObjectUtil.isNotEmpty(topic.getUserAnswer()));
    }

    /**
     * 处理信息和表单提交并下一步
     * 计算系统评分，创建流程记录，更新进度
     *
     * @param riskEvaluateContext 风险评估上下文
     * @param progressValue       进度值
     */
    private void handleInfoAndFormSubmission(RiskEvaluateContext riskEvaluateContext, int progressValue) {
        String evaluateId = riskEvaluateContext.getRiskEvaluateManage().getId();
        log.info("开始处理信息和表单提交，评估ID: {}", evaluateId);

        // 执行系统评分计算
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("riskSystemScoreChain", null, riskEvaluateContext);
        if (!liteflowResponse.isSuccess()) {
            String errorMsg = "系统评分计算失败: " + liteflowResponse.getMessage();
            log.error("评估ID: {}, {}", evaluateId, errorMsg);
            throw new ServiceException(500, errorMsg);
        }
        //保存指标评分记录
        riskEvaluateIndexRecordService.saveIndexRecord(riskEvaluateContext.getProcessedIndexList(), evaluateId);

        // 创建人工评分流程记录
        RiskEvaluateManage evaluateManage = riskEvaluateContext.getRiskEvaluateManage();
        RiskProcessRecordParam processParam = new RiskProcessRecordParam();
        processParam.setSystemScore(evaluateManage.getSystemScore());
        processParam.setManualScore(evaluateManage.getManualScore());
        riskEvaluateContext.setProcessedIndexList(null);
        riskProcessRecordService.updateProcessProgress(evaluateManage.getId(), RiskProcessConstants.ProcessStep.MANUAL_SYSTEM_SCORING_CODE, RiskProcessConstants.ProcessStatus.COMPLETED, processParam, riskEvaluateContext);

        // 更新进度
        evaluateManage.setProgress(progressValue);
        log.info("信息和表单提交处理完成，评估ID: {}, 系统得分: {}, 进度: {}%",
                evaluateId, evaluateManage.getSystemScore(), progressValue);
    }

    /**
     * 处理人工评分提交并下一步
     * 验证人工评分，计算总分和风险等级，完成评估
     *
     * @param riskEvaluateContext    风险评估上下文
     * @param progressValue          进度值
     * @param statusValue            状态值
     * @param manualScoreRequiredMsg 人工评分必填提示信息
     */
    private void handleManualScoreSubmission(RiskEvaluateContext riskEvaluateContext,
                                             int progressValue,
                                             String statusValue,
                                             String manualScoreRequiredMsg) {
        RiskEvaluateManage evaluateManage = riskEvaluateContext.getRiskEvaluateManage();
        String evaluateId = evaluateManage.getId();
        log.info("开始处理人工评分提交，评估ID: {}", evaluateId);

        // 验证人工评分必填
        if (ObjectUtil.isEmpty(evaluateManage.getManualScore())) {
            log.warn("人工评分为空，评估ID: {}", evaluateId);
            throw new ServiceException(500, manualScoreRequiredMsg);
        }

        // 计算评估总分 = 系统得分 + 人工评分
        BigDecimal systemScore = evaluateManage.getSystemScore();
        BigDecimal manualScore = evaluateManage.getManualScore();
        BigDecimal totalScore = systemScore.add(manualScore);
        evaluateManage.setScore(totalScore);

        log.info("计算评估总分，评估ID: {}, 系统得分: {}, 人工得分: {}, 总得分: {}",
                evaluateId, systemScore, manualScore, totalScore);

        // 执行风险等级计算
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("riskLevelCalculationChain", null, riskEvaluateContext);
        if (!liteflowResponse.isSuccess()) {
            String errorMsg = "风险等级计算失败: " + liteflowResponse.getMessage();
            log.error("评估ID: {}, {}", evaluateId, errorMsg);
            throw new ServiceException(500, errorMsg);
        }

        // 创建评估结果流程记录
        RiskProcessRecordParam processParam = new RiskProcessRecordParam();
        processParam.setTotalScore(totalScore);
        processParam.setManualScore(manualScore);
        processParam.setRiskLevel(riskEvaluateContext.getRiskEvaluateManage().getRiskLevel());
        riskEvaluateContext.setProcessedIndexList(null);
        riskProcessRecordService.updateProcessProgress(evaluateManage.getId(), RiskProcessConstants.ProcessStep.MANUAL_SYSTEM_SCORING_CODE, RiskProcessConstants.ProcessStatus.COMPLETED, processParam, riskEvaluateContext);
        riskProcessRecordService.updateProcessProgress(evaluateManage.getId(), RiskProcessConstants.ProcessStep.ASSESSMENT_RESULT_CODE, RiskProcessConstants.ProcessStatus.COMPLETED, processParam, riskEvaluateContext);

        // 更新进度和状态为完成
        evaluateManage.setProgress(progressValue);
        evaluateManage.setStatus(statusValue);

        log.info("人工评分提交处理完成，评估ID: {}, 风险等级: {}, 进度: {}%, 状态: 完成",
                evaluateId, evaluateManage.getRiskLevel(), progressValue);
    }

    @Override
    public RiskTranscriptManagement detailTranscript(RiskTranscriptManagementParam riskTranscriptManagementParam) {
        return riskTranscriptManagementService.detail(riskTranscriptManagementParam);
    }

    @Override
    public void downloadPdf(String id, HttpServletResponse response) {
        RiskTranscriptManagementParam riskTranscriptManagementParam = new RiskTranscriptManagementParam();
        riskTranscriptManagementParam.setId(id);
        fillPdfTemplate(riskTranscriptManagementParam, response);
    }

    @Override
    public void downloadWord(String id, HttpServletResponse response) {
        RiskTranscriptManagementParam riskTranscriptManagementParam = new RiskTranscriptManagementParam();
        riskTranscriptManagementParam.setId(id);
        fillWordTemplate(riskTranscriptManagementParam, response);
    }

    @Override
    public void deleteTranscript(RiskTranscriptManagementParam riskTranscriptManagementParam) {
        riskTranscriptManagementService.delete(riskTranscriptManagementParam);
    }

    @Override
    public void editTranscript(RiskTranscriptManagementParam riskTranscriptManagementParam) {
        RiskTranscriptManagement riskTranscriptManagement = riskTranscriptManagementService.getById(riskTranscriptManagementParam.getId());
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("riskTranscriptHandelChain", null, riskTranscriptManagementParam, riskTranscriptManagement);
        if (!liteflowResponse.isSuccess()) {
            throw new ServiceException(500, liteflowResponse.getMessage());
        } else {
            riskTranscriptManagementService.updateById(riskTranscriptManagement);
        }
    }

    @Override
    public void fillPdfTemplate(RiskTranscriptManagementParam riskTranscriptManagementParam, HttpServletResponse response) {
        try {
            // 获取笔录信息
            RiskTranscriptManagement riskTranscriptManagement = riskTranscriptManagementService.getRiskTranscriptManagement(riskTranscriptManagementParam.getId());

            // 获取并解析笔录内容
            PaperMaintenanceParam paperMaintenance = riskTranscriptManagementService.parseTranscriptContent(riskTranscriptManagement.getTranscriptJson());

            // 获取基本信息
            TranscriptBasicInfo basicInfo = riskTranscriptManagementService.parseBasicInfo(riskTranscriptManagement.getBasicInfoJson());

            // 获取题目列表
            List<PaperTopicParam> topicParamList = paperMaintenance.getTopicList();
            if (CollUtil.isEmpty(topicParamList)) {
                throw new ServiceException(500, "笔录题目列表为空");
            }

            // 选择模板路径
            String templatePath = riskTranscriptManagementService.getTemplatePathByTemplateId(riskTranscriptManagement.getTemplateId());

            // 转换题目为问答格式
            List<Map<String, Object>> questionAnswerList = riskTranscriptManagementService.convertTopicsToQuestionAnswers(topicParamList);

            // 生成PDF文档
            String paperTitle = ObjectUtil.isNotEmpty(paperMaintenance.getBlTypeName()) ?
                    paperMaintenance.getBlTypeName() : "危险性评估笔录";
            String fileName = ObjectUtil.isNotEmpty(riskTranscriptManagement.getTranscriptName()) ?
                    riskTranscriptManagement.getTranscriptName() : "笔录";

            WordUtil.exportQuestionsWithAnswersAndObjectAsPdf(
                    response,
                    fileName,
                    templatePath,
                    paperTitle,
                    questionAnswerList,
                    basicInfo
            );

            log.info("导出笔录PDF成功，笔录ID：{}", riskTranscriptManagementParam.getId());
        } catch (Exception e) {
            log.error("导出笔录PDF失败，笔录ID：{}", riskTranscriptManagementParam.getId(), e);
            throw new ServiceException(500, "导出笔录PDF失败：" + e.getMessage());
        }
    }

    @Override
    public void fillWordTemplate(RiskTranscriptManagementParam riskTranscriptManagementParam, HttpServletResponse response) {
        try {
            // 获取笔录信息
            RiskTranscriptManagement riskTranscriptManagement = riskTranscriptManagementService.getRiskTranscriptManagement(riskTranscriptManagementParam.getId());

            // 获取并解析笔录内容
            PaperMaintenanceParam paperMaintenance = riskTranscriptManagementService.parseTranscriptContent(riskTranscriptManagement.getTranscriptJson());

            // 获取基本信息
            TranscriptBasicInfo basicInfo = riskTranscriptManagementService.parseBasicInfo(riskTranscriptManagement.getBasicInfoJson());

            // 获取题目列表
            List<PaperTopicParam> topicParamList = paperMaintenance.getTopicList();
            if (CollUtil.isEmpty(topicParamList)) {
                throw new ServiceException(500, "笔录题目列表为空");
            }

            // 选择模板路径
            String templatePath = riskTranscriptManagementService.getTemplatePathByTemplateId(riskTranscriptManagement.getTemplateId());

            // 转换题目为问答格式
            List<Map<String, Object>> questionAnswerList = riskTranscriptManagementService.convertTopicsToQuestionAnswers(topicParamList);

            // 生成Word文档
            String paperTitle = ObjectUtil.isNotEmpty(paperMaintenance.getBlTypeName()) ?
                    paperMaintenance.getBlTypeName() : "危险性评估笔录";
            String fileName = ObjectUtil.isNotEmpty(riskTranscriptManagement.getTranscriptName()) ?
                    riskTranscriptManagement.getTranscriptName() : "笔录";

            WordUtil.exportQuestionsWithAnswersAndObjectToTemplate(
                    response,
                    fileName,
                    templatePath,
                    paperTitle,
                    questionAnswerList,
                    basicInfo
            );

            log.info("导出笔录Word成功，笔录ID：{}", riskTranscriptManagementParam.getId());
        } catch (Exception e) {
            log.error("导出笔录Word失败，笔录ID：{}", riskTranscriptManagementParam.getId(), e);
            throw new ServiceException(500, "导出笔录Word失败：" + e.getMessage());
        }
    }

    /**
     * 获取笔录信息
     *
     * @param transcriptId 笔录ID
     * @return 笔录信息
     */
    private RiskTranscriptManagement getRiskTranscriptManagement(String transcriptId) {
        if (ObjectUtil.isEmpty(transcriptId)) {
            throw new ServiceException(500, "笔录ID不能为空");
        }
        RiskTranscriptManagement riskTranscriptManagement = riskTranscriptManagementService.getById(transcriptId);
        if (ObjectUtil.isNull(riskTranscriptManagement)) {
            throw new ServiceException(500, TRANSCRIPT_NOT_EXIST);
        }
        return riskTranscriptManagement;
    }

    /**
     * 生成PDF文件
     *
     * @param riskTranscriptManagementParam 笔录管理参数
     * @return PDF文件
     */
    private File generatePdfFile(RiskTranscriptManagementParam riskTranscriptManagementParam) {
        return riskTranscriptManagementService.generatePdfFile(riskTranscriptManagementParam);
    }

    /**
     * 将PDF文件上传到OSS
     *
     * @param pdfFile  PDF文件
     * @param fileName 文件名
     * @return 文件信息
     */
    private SysFileInfo uploadPdfToOss(File pdfFile, String fileName) {
        return riskTranscriptManagementService.uploadPdfToOss(pdfFile, fileName);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addTranscript(String id, String transcriptId) {
        String[] split = transcriptId.split(",");
        for (String id1 : split) {
            //添加笔录
            RiskTranscriptManagement riskTranscriptManagement = riskTranscriptManagementService.getById(id1);
            if (ObjectUtil.isNull(riskTranscriptManagement)) {
                throw new ServiceException(500, TRANSCRIPT_NOT_EXIST);
            }
            if (ObjectUtil.isNotEmpty(riskTranscriptManagement.getPid()) && !riskTranscriptManagement.getPid().equals(id)) {
                throw new ServiceException(500, "该笔录已经关联到其他评估");
            }
            riskTranscriptManagement.setPid(id);
            riskTranscriptManagementService.updateById(riskTranscriptManagement);
        }
    }

    @Override
    public void addTemplate(String id, String templateId) {
        //添加之前查询已有模板，判断是否已经添加了同类模板
        int count = riskTranscriptManagementService.count(new QueryWrapper<RiskTranscriptManagement>().lambda().eq(RiskTranscriptManagement::getPid, id).eq(RiskTranscriptManagement::getTemplateId, templateId));
        if (count > 0) {
            throw new ServiceException(500, "已存在同类型的笔录模板");
        }
        PaperMaintenanceParam paperMaintenanceParam = new PaperMaintenanceParam();
        paperMaintenanceParam.setId(templateId);
        PaperMaintenance paperMaintenance = paperMaintenanceService.detail(paperMaintenanceParam);
        if (ObjectUtil.isNull(paperMaintenance)) {
            throw new ServiceException(500, "模板不存在");
        }
        addRiskTranscriptManagements(paperMaintenance, id, templateId);
    }

    private void addRiskTranscriptManagements(PaperMaintenance paperMaintenance, String id, String templateId) {
        RiskTranscriptManagement riskTranscriptManagement = new RiskTranscriptManagement();
        riskTranscriptManagement.setJzdxId(id);
        riskTranscriptManagement.setTranscriptName(paperMaintenance.getTitle());
        riskTranscriptManagement.setTranscriptType(paperMaintenance.getPaperType());
        riskTranscriptManagement.setPid(id);
        riskTranscriptManagement.setDelFlag("0");
        riskTranscriptManagement.setStatus("0");
        riskTranscriptManagement.setTemplateId(templateId);
        riskTranscriptManagement.setTranscriptJson(JSON.toJSONString(paperMaintenance));
        riskTranscriptManagementService.save(riskTranscriptManagement);
    }

    @Override
    public RiskEvaluateContext generateEvaluateByType(String id, String evaluateType) {
        CorrectionObjectInformation objectInformation = correctionObjectInformationService.getById(id);
        if (ObjectUtil.isNull(objectInformation)) {
            throw new ServiceException(500, "矫正对象不存在");
        }
        RiskEvaluateContext riskEvaluateContext = new RiskEvaluateContext();
        riskEvaluateContext.setSource("1");
        generateEvaluateByPerson(objectInformation, evaluateType, riskEvaluateContext);
        this.save(riskEvaluateContext.getRiskEvaluateManage());
        riskTranscriptManagementService.saveBatch(riskEvaluateContext.getRiskTranscriptManagementList());
        return riskEvaluateContext;
    }

    @Override
    public TranscriptBasicInfo generateBasicInfo(String id, String transcriptId) {
        RiskTranscriptManagement riskTranscriptManagement = riskTranscriptManagementService.getById(transcriptId);
        if (ObjectUtil.isNull(riskTranscriptManagement)) {
            throw new ServiceException(500, TRANSCRIPT_NOT_EXIST);
        }
        TranscriptBasicInfo transcriptBasicInfo = new TranscriptBasicInfo();
        //当前年月日预填充
        int year = DateUtil.thisYear();
        int month = DateUtil.thisMonth() + 1;
        int day = DateUtil.thisDayOfMonth();
        int hour = DateUtil.thisHour(true);
        transcriptBasicInfo.setStartYear(String.valueOf(year));
        transcriptBasicInfo.setStartMonth(String.valueOf(month));
        transcriptBasicInfo.setStartDay(String.valueOf(day));
        transcriptBasicInfo.setStartHour(String.valueOf(hour));

        transcriptBasicInfo.setEndYear(String.valueOf(year));
        transcriptBasicInfo.setEndMonth(String.valueOf(month));
        transcriptBasicInfo.setEndDay(String.valueOf(day));
        transcriptBasicInfo.setEndHour(String.valueOf(hour));

        transcriptBasicInfo.setEYear(String.valueOf(year));
        transcriptBasicInfo.setEMonth(String.valueOf(month));
        transcriptBasicInfo.setEDay(String.valueOf(day));
        //填充矫正对象姓名
        RiskEvaluateManage riskEvaluateManage = this.getById(id);
        transcriptBasicInfo.setXm(riskEvaluateManage.getXm());
        //谈话人和记录人写当前用户的姓名
        try {
            transcriptBasicInfo.setThr(LoginContextHolder.me().getSysLoginUser().getName());
            transcriptBasicInfo.setJlr(LoginContextHolder.me().getSysLoginUser().getName());
        } catch (Exception e) {
            log.error("获取当前用户姓名失败", e);
        }

        //填充矫正对象基本信息
        CorrectionObjectInformation objectInformation = correctionObjectInformationService.getById(riskEvaluateManage.getJzdxId());
        if (ObjectUtil.isNotEmpty(objectInformation)) {
            //填充矫正对象基本信息
            transcriptBasicInfo.setXm(objectInformation.getXm());
            transcriptBasicInfo.setXb(objectInformation.getXbName());
            transcriptBasicInfo.setJzlb(objectInformation.getJzlbName());
            if (ObjectUtil.isNotEmpty(objectInformation.getCsrq())) {
                transcriptBasicInfo.setCsrq(DateUtil.format(objectInformation.getCsrq(), "yyyy-MM-dd"));
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getMzName())) {
                transcriptBasicInfo.setMz(objectInformation.getMzName());
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getWhcdName())) {
                transcriptBasicInfo.setWhcd(objectInformation.getWhcdName());
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getHyzkName())) {
                transcriptBasicInfo.setHyzk(objectInformation.getHyzkName());
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getJtzmName())) {
                transcriptBasicInfo.setZm(objectInformation.getJtzmName());
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getYqtxqx())) {
                transcriptBasicInfo.setXq(objectInformation.getYqtxqx());
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getDwhm())) {
                transcriptBasicInfo.setLxdh(objectInformation.getDwhm());
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getXgzdwName())) {
                transcriptBasicInfo.setGzdw(objectInformation.getXgzdwName());
            }
            //拼接户籍地址
            StringBuilder hjAddress = new StringBuilder();
            if (ObjectUtil.isNotEmpty(objectInformation.getHjszsName())) {
                hjAddress.append(objectInformation.getHjszsName());
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getHjszdsName())) {
                hjAddress.append(objectInformation.getHjszdsName());
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getHjszxqName())) {
                hjAddress.append(objectInformation.getHjszxqName());
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getHjszdName())) {
                hjAddress.append(objectInformation.getHjszdName());
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getHjszdmx())) {
                hjAddress.append(objectInformation.getHjszdmx());
            }
            if (hjAddress.length() > 0) {
                transcriptBasicInfo.setHjszd(hjAddress.toString());
            }

            //现住址
            StringBuilder xzAddress = new StringBuilder();
            if (ObjectUtil.isNotEmpty(objectInformation.getGdjzdszsName())) {
                xzAddress.append(objectInformation.getGdjzdszsName());
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getGdjzdszdsName())) {
                xzAddress.append(objectInformation.getGdjzdszdsName());
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getGdjzdszxqName())) {
                xzAddress.append(objectInformation.getGdjzdszxqName());
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getGdjzdName())) {
                xzAddress.append(objectInformation.getGdjzdName());
            }
            if (ObjectUtil.isNotEmpty(objectInformation.getGdjzdmx())) {
                xzAddress.append(objectInformation.getGdjzdmx());
            }
            if (xzAddress.length() > 0) {
                transcriptBasicInfo.setXzz(xzAddress.toString());
            }
        }

        return transcriptBasicInfo;
    }

    @Override
    public void sendRiskEvaluateZZDNotification(RiskEvaluateManage riskEvaluateManage) {
        try {
//            log.info("开始发送风险评估浙政钉提醒通知，对象：{}", riskEvaluateManage.getXm());
//
//            // 创建上下文并设置数据
//            DefaultContext context = new DefaultContext();
//            context.setData("riskEvaluateManage", riskEvaluateManage);
//
//            // 执行浙政钉通知流程链
//            // 流程：RiskZZDNotice -> RiskZZDRecordGenCmp -> RiskZZDCanSendCmp -> RiskZZDRecordSendCmp
//            LiteflowResponse response = flowExecutor.execute2Resp("riskZZDNotificationChain", null, riskEvaluateManage, context);
//
//            if (response.isSuccess()) {
//                log.info("风险评估浙政钉提醒通知发送成功，对象：{}", riskEvaluateManage.getXm());
//            } else {
//                log.error("风险评估浙政钉提醒通知发送失败，对象：{}，原因：{}", riskEvaluateManage.getXm(), response.getMessage());
//            }

        } catch (Exception e) {
            log.error("发送风险评估浙政钉提醒通知异常，对象：{}", riskEvaluateManage.getXm(), e);
        }
    }

    @Override
    public void exportIndexList(RiskEvaluateManageParam riskEvaluateManageParam, HttpServletResponse response) {
        if (ObjectUtil.isEmpty(riskEvaluateManageParam.getId())) {
            throw new ServiceException(500, "评估ID不能为空");
        }
        //找到评估对应的流程节点的完整json
        RiskEvaluateManage riskEvaluateManage = this.getById(riskEvaluateManageParam.getId());
        if (ObjectUtil.isNull(riskEvaluateManage)) {
            throw new ServiceException(500, "评估不存在");
        }
        RiskEvaluateIndexRecord indexRecord = riskEvaluateIndexRecordService.getById(riskEvaluateManageParam.getId());
        if (ObjectUtil.isNotEmpty(indexRecord)) {

            List<RiskIndexManage> indexList = JSON.parseArray(indexRecord.getIndexJson(), RiskIndexManage.class);
            if (CollUtil.isNotEmpty(indexList)) {
                //导出
                ExportParams exportParams = new ExportParams();
                exportParams.setTitle("评估指标");
                exportParams.setSheetName("评估指标");
                try {
                    // 使用关联合并功能：得分列跟随三级指标列的合并模式
                    Workbook workbook = RiskIndexExcelExportUtil.exportRiskIndexWithAssociatedMerge(exportParams, indexList);
                    response.setContentType("application/vnd.ms-excel");
                    String fileName = URLEncoder.encode("评估指标.xlsx", "UTF-8");
                    response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
                    workbook.write(response.getOutputStream());
                    log.info("评估指标导出成功，数据量: {}，得分列已跟随三级指标列进行关联合并", indexList.size());
                } catch (IOException e) {
                    log.error("导出评估指标失败", e);
                }
            }

        } else {
            log.info("指标记录不存在");
            throw new ServiceException(500, "指标记录不存在");
        }

    }

    @Override
    public String getLastRiskLevel(CorrectionObjectInformation correctionObjectInformation) {
        List<RiskEvaluateManage> list = this.list(new QueryWrapper<RiskEvaluateManage>().lambda().eq(RiskEvaluateManage::getJzdxId, correctionObjectInformation.getId()).orderByDesc(RiskEvaluateManage::getCreateTime).last("limit 1"));
        if (CollUtil.isNotEmpty(list)) {
            return list.get(0).getRiskLevel();
        }
        return null;
    }

    @Override
    public List<EvaluationDetailDto> getEvaluationDetails(String evaluationManageId) {
        log.info("开始查询评估详情，评估管理ID: {}", evaluationManageId);

        try {
            // 使用LiteFlow执行评估详情查询链
            LiteflowResponse response = flowExecutor.execute2Resp("evaluationDetailsChain", evaluationManageId);

            if (response.isSuccess()) {
                @SuppressWarnings("unchecked")
                List<EvaluationDetailDto> result = response.getContextBean(List.class);
                log.info("查询评估详情成功，共找到 {} 条记录", result != null ? result.size() : 0);
                return result != null ? result : CollUtil.newArrayList();
            } else {
                log.error("查询评估详情失败: {}", response.getMessage());
                return CollUtil.newArrayList();
            }

        } catch (Exception e) {
            log.error("查询评估详情时发生异常，评估管理ID: {}", evaluationManageId, e);
            return CollUtil.newArrayList();
        }
    }
}
