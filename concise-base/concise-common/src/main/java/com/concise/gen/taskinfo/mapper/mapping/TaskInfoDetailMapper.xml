<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.taskinfo.mapper.TaskInfoDetailMapper">


    <resultMap id="result" type="com.concise.gen.taskinfo.result.TaskInfoDetailResult">
        <result column="TASK_NUM" property="taskNum" />
        <result column="PERSON_NUM" property="personNum" />
        <result column="CALLED_NUM" property="calledNum" />
        <result column="ANSWERED_NUM" property="answeredNum" />
        <result column="PASS_AUDIO_CHECK_NUM" property="passAudioCheckNum" />
    </resultMap>

    <select id="taskCount" resultMap="result">
        select count(*) as TASK_NUM,
               COUNT(DISTINCT sqjzry_tel) as PERSON_NUM,
               sum(case when call_ret != '00' then 1 else 0 end) as CALLED_NUM,
               sum(case when call_ret = '02' then 1 else 0 end) as  ANSWERED_NUM,
               sum(case when audio_check = 'Y' then 1 else 0 end) as PASS_AUDIO_CHECK_NUM
        from task_info_detail
        ${ew.customSqlSegment}
    </select>

    <select id="taskStatus" resultType="map">
        select sum(case when call_ret = '00' then 1 else 0 end) as dwh
        from task_info_detail
                 ${ew.customSqlSegment}
    </select>

    <select id="getDetail" resultType="com.concise.gen.taskinfo.entity.TaskInfoDetail">
        select * from task_info_detail
        where sqjzry_tel = #{param1} and task_name = #{param2}
        limit 1
    </select>
    <select id="getDetailByCallId" resultType="com.concise.gen.taskinfo.entity.TaskInfoDetail">
        select * from task_info_detail
        where call_id = #{param1}
            limit 1

    </select>
    <select id="getKeyPeriod" resultType="com.concise.common.pojo.node.AntdBaseTreeNode">
            SELECT
                id AS id,
                1 AS parent_id,
                NAME AS title,
                NAME AS value
            FROM
                correction_sec_config
            WHERE
                `status` = '1'
              AND sec_type = 'zdsd'
    </select>
    <select id="getPointList" resultType="com.concise.gen.taskinfo.entity.CorrectionSecConfig">
        SELECT
            *
        FROM
            correction_sec_config
        WHERE
            `status` = '1'
          AND sec_type = 'zdsd'
    </select>
</mapper>
