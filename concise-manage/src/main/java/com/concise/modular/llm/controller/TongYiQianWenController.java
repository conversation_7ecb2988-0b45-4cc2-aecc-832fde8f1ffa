package com.concise.modular.llm.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.modular.llm.dto.ChatAdvancedRequest;
import com.concise.modular.llm.dto.ChatRequest;
import com.concise.modular.llm.dto.TongYiQianWenRequest;
import com.concise.modular.llm.service.TongYiQianWenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 通义千问API控制器
 */
@Slf4j
@Api(tags = "通义千问API")
@RestController
@RequestMapping("/llm/tongyi")
public class TongYiQianWenController {

    @Resource
    private TongYiQianWenService tongYiQianWenService;

    @PostMapping("/chat")
    @ApiOperation("单轮对话")
    @BusinessLog(title = "通义千问_对话", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData chat(@RequestBody ChatRequest request) {
        String response = tongYiQianWenService.chat(request.getPrompt(), request.getModelName());
        return new SuccessResponseData(response);
    }

    private final ConcurrentMap<String, SseEmitter> emitters = new ConcurrentHashMap<>();

    @PostMapping("/chat/stream")
    @ApiOperation("单轮对话(流式)")
    @BusinessLog(title = "通义千问_流式对话", opType = LogAnnotionOpTypeEnum.OTHER)
    public SseEmitter chatStream(@RequestBody ChatRequest request) {
        // 创建新的SSE发射器，设置超时时间为5分钟
        SseEmitter emitter = new SseEmitter(5 * 60 * 1000L);
        String emitterId = String.valueOf(System.currentTimeMillis());
        emitters.put(emitterId, emitter);

        // 添加完成与错误回调
        emitter.onCompletion(() -> emitters.remove(emitterId));
        emitter.onTimeout(() -> emitters.remove(emitterId));
        emitter.onError(e -> emitters.remove(emitterId));

        // 调用服务进行流式输出
        tongYiQianWenService.chatStream(request.getPrompt(), request.getModelName(), null, null, null, message -> {
            try {
                if ("[DONE]".equals(message)) {
                    emitter.complete();
                } else if (message.startsWith("[ERROR]")) {
                    emitter.completeWithError(new RuntimeException(message));
                } else {
                    emitter.send(message);
                }
            } catch (IOException e) {
                log.error("发送流式数据失败", e);
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    @PostMapping("/chat/history")
    @ApiOperation("多轮对话")
    @BusinessLog(title = "通义千问_多轮对话", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData chatWithHistory(
            @RequestBody List<TongYiQianWenRequest.Message> messages,
            @RequestParam(required = false) String modelName) {
        String response = tongYiQianWenService.chatWithHistory(messages, modelName);
        return new SuccessResponseData(response);
    }

    @PostMapping("/chat/advanced")
    @ApiOperation("高级对话(可配置参数)")
    @BusinessLog(title = "通义千问_高级对话", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData chatAdvanced(@RequestBody ChatAdvancedRequest request) {
        String response = tongYiQianWenService.chat(
                request.getPrompt(),
                request.getModelName(),
                request.getMaxTokens(),
                request.getTemperature(),
                request.getTopP());
        return new SuccessResponseData(response);
    }

    @PostMapping("/chat/stream/string")
    @ApiOperation("单轮对话(流式转字符串)")
    @BusinessLog(title = "通义千问_流式转字符串对话", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData chatStreamToString(@RequestBody ChatRequest request) {
        String response = tongYiQianWenService.chatStreamToString(request.getPrompt(), request.getModelName());
        return new SuccessResponseData(response);
    }

    @PostMapping("/chat/stream/string/advanced")
    @ApiOperation("高级对话(流式转字符串,可配置参数)")
    @BusinessLog(title = "通义千问_高级流式转字符串对话", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData chatStreamToStringAdvanced(@RequestBody ChatAdvancedRequest request) {
        String response = tongYiQianWenService.chatStreamToString(
                request.getPrompt(),
                request.getModelName(),
                request.getMaxTokens(),
                request.getTemperature(),
                request.getTopP());
        return new SuccessResponseData(response);
    }
} 