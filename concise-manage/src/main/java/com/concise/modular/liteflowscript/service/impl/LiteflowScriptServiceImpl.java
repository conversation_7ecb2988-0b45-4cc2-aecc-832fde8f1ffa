package com.concise.modular.liteflowscript.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.liteflowscript.entity.LiteflowScript;
import com.concise.modular.liteflowscript.enums.LiteflowScriptExceptionEnum;
import com.concise.modular.liteflowscript.mapper.LiteflowScriptMapper;
import com.concise.modular.liteflowscript.param.LiteflowScriptParam;
import com.concise.modular.liteflowscript.service.LiteflowScriptService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * 规则引擎脚本service接口实现类
 *
 * <AUTHOR>
 * @date 2025-05-08 16:44:31
 */
@Service
public class LiteflowScriptServiceImpl extends ServiceImpl<LiteflowScriptMapper, LiteflowScript> implements LiteflowScriptService {

    @Override
    public PageResult<LiteflowScript> page(LiteflowScriptParam liteflowScriptParam) {
        QueryWrapper<LiteflowScript> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(liteflowScriptParam)) {

            // 根据 查询
            if (ObjectUtil.isNotEmpty(liteflowScriptParam.getApplicationName())) {
                queryWrapper.lambda().eq(LiteflowScript::getApplicationName, liteflowScriptParam.getApplicationName());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(liteflowScriptParam.getScriptId())) {
                queryWrapper.lambda().eq(LiteflowScript::getScriptId, liteflowScriptParam.getScriptId());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(liteflowScriptParam.getScriptName())) {
                queryWrapper.lambda().eq(LiteflowScript::getScriptName, liteflowScriptParam.getScriptName());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(liteflowScriptParam.getScriptData())) {
                queryWrapper.lambda().eq(LiteflowScript::getScriptData, liteflowScriptParam.getScriptData());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(liteflowScriptParam.getScriptType())) {
                queryWrapper.lambda().eq(LiteflowScript::getScriptType, liteflowScriptParam.getScriptType());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(liteflowScriptParam.getLanguage())) {
                queryWrapper.lambda().eq(LiteflowScript::getLanguage, liteflowScriptParam.getLanguage());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(liteflowScriptParam.getEnable())) {
                queryWrapper.lambda().eq(LiteflowScript::getEnable, liteflowScriptParam.getEnable());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<LiteflowScript> list(LiteflowScriptParam liteflowScriptParam) {
        return this.list();
    }

    @Override
    public void add(LiteflowScriptParam liteflowScriptParam) {
        LiteflowScript liteflowScript = new LiteflowScript();
        BeanUtil.copyProperties(liteflowScriptParam, liteflowScript);
        this.save(liteflowScript);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(LiteflowScriptParam liteflowScriptParam) {
        this.removeById(liteflowScriptParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(LiteflowScriptParam liteflowScriptParam) {
        LiteflowScript liteflowScript = this.queryLiteflowScript(liteflowScriptParam);
        BeanUtil.copyProperties(liteflowScriptParam, liteflowScript);
        this.updateById(liteflowScript);
    }

    @Override
    public LiteflowScript detail(LiteflowScriptParam liteflowScriptParam) {
        return this.queryLiteflowScript(liteflowScriptParam);
    }

    /**
     * 获取规则引擎脚本
     *
     * <AUTHOR>
     * @date 2025-05-08 16:44:31
     */
    private LiteflowScript queryLiteflowScript(LiteflowScriptParam liteflowScriptParam) {
        LiteflowScript liteflowScript = this.getById(liteflowScriptParam.getId());
        if (ObjectUtil.isNull(liteflowScript)) {
            throw new ServiceException(LiteflowScriptExceptionEnum.NOT_EXIST);
        }
        return liteflowScript;
    }
}
