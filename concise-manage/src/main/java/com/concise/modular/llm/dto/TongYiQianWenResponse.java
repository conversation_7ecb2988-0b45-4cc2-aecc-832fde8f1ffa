package com.concise.modular.llm.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通义千问响应结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TongYiQianWenResponse {

    /**
     * 选择结果列表
     */
    private List<Choice> choices;

    /**
     * 对象类型
     */
    private String object;

    /**
     * 使用的令牌数统计
     */
    private Usage usage;

    /**
     * 创建时间
     */
    private Long created;

    /**
     * 系统指纹
     */
    private String systemFingerprint;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 请求ID
     */
    private String id;

    /**
     * 错误代码
     */
    private String code;

    /**
     * 错误消息
     */
    private String message;

    /**
     * 创建错误响应
     *
     * @param errorMsg 错误信息
     * @return 错误响应对象
     */
    public static TongYiQianWenResponse error(String errorMsg) {
        TongYiQianWenResponse response = new TongYiQianWenResponse();
        response.setCode("ERROR");
        response.setMessage(errorMsg);
        return response;
    }

    /**
     * 判断响应是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return code == null || "SUCCESS".equals(code);
    }

    /**
     * 获取响应文本内容
     *
     * @return 文本内容，如果响应失败则返回错误信息
     */
    public String getContent() {
        if (!isSuccess()) {
            return "[ERROR] " + message;
        }
        
        if (choices != null && !choices.isEmpty()) {
            Choice choice = choices.get(0);
            if (choice.getMessage() != null) {
                return choice.getMessage().getContent();
            }
        }
        
        return "";
    }

    /**
     * 选择结果类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Choice {
        /**
         * 消息内容
         */
        private Message message;
        
        /**
         * 结束原因
         */
        @JSONField(name = "finish_reason")
        private String finishReason;

        /**
         * 索引
         */
        private Integer index;

        /**
         * 日志概率
         */
        private Object logprobs;
        
        /**
         * 增量内容（流式输出时使用）
         */
        private Delta delta;
    }

    /**
     * 消息类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Message {
        /**
         * 角色：system, user, assistant
         */
        private String role;
        
        /**
         * 消息内容
         */
        private String content;
    }

    /**
     * 增量内容类（流式输出时使用）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Delta {
        /**
         * 角色：system, user, assistant
         */
        private String role;
        
        /**
         * 消息内容
         */
        private String content;
    }

    /**
     * 令牌使用统计类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Usage {
        /**
         * 提示令牌数
         */
        @JSONField(name = "prompt_tokens")
        private Integer promptTokens;
        
        /**
         * 补全令牌数
         */
        @JSONField(name = "completion_tokens")
        private Integer completionTokens;
        
        /**
         * 总令牌数
         */
        @JSONField(name = "total_tokens")
        private Integer totalTokens;

        /**
         * 提示令牌详情
         */
        @JSONField(name = "prompt_tokens_details")
        private PromptTokensDetails promptTokensDetails;
    }

    /**
     * 提示令牌详情类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PromptTokensDetails {
        /**
         * 缓存令牌数
         */
        @JSONField(name = "cached_tokens")
        private Integer cachedTokens;
    }
}
