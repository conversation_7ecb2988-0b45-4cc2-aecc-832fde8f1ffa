package com.concise.modular.risk.riskevaluatemanage.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.modular.risk.riskevaluatemanage.entity.RiskEvaluateContext;
import com.concise.modular.risk.riskevaluatemanage.entity.RiskEvaluateManage;
import com.concise.modular.risk.riskevaluatemanage.param.RiskEvaluateManageParam;
import com.concise.modular.risk.riskevaluatemanage.service.RiskEvaluateManageService;
import com.concise.modular.risk.risktranscriptmanagement.param.RiskTranscriptManagementParam;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 危险性评估管理控制器
 *
 * <AUTHOR>
 * @date 2025-07-14 15:58:13
 */
@Api(tags = "危险性评估管理")
@RestController
public class RiskEvaluateManageController {

    @Resource
    private RiskEvaluateManageService riskEvaluateManageService;

    /**
     * 查询危险性评估管理
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    @Permission
    @GetMapping("/riskEvaluateManage/page")
    @ApiOperation("危险性评估管理_分页查询")
    @BusinessLog(title = "危险性评估管理_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(RiskEvaluateManageParam riskEvaluateManageParam) {
        return new SuccessResponseData(riskEvaluateManageService.page(riskEvaluateManageParam));
    }

    /**
     * 添加危险性评估管理
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    @Permission
    @PostMapping("/riskEvaluateManage/add")
    @ApiOperation("危险性评估管理_增加")
    @BusinessLog(title = "危险性评估管理_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(RiskEvaluateManageParam.add.class) RiskEvaluateManageParam riskEvaluateManageParam) {
        riskEvaluateManageService.add(riskEvaluateManageParam);
        return new SuccessResponseData();
    }

    /**
     * 根据矫正对象id初始化评估数据
     */
    @Permission
    @GetMapping("/riskEvaluateManage/initById")
    @ApiOperation("危险性评估管理_根据矫正对象id初始化评估数据")
    @BusinessLog(title = "危险性评估管理_根据矫正对象id初始化评估数据", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData initById(@RequestParam String id) {
        RiskEvaluateManage riskEvaluateManage = riskEvaluateManageService.initEvaluateManage(id);
        return new SuccessResponseData(riskEvaluateManage);
    }

    /**
     * 删除危险性评估管理
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    @Permission
    @PostMapping("/riskEvaluateManage/delete")
    @ApiOperation("危险性评估管理_删除")
    @BusinessLog(title = "危险性评估管理_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(RiskEvaluateManageParam.delete.class) RiskEvaluateManageParam riskEvaluateManageParam) {
        riskEvaluateManageService.delete(riskEvaluateManageParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑危险性评估管理
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    @Permission
    @PostMapping("/riskEvaluateManage/edit")
    @ApiOperation("危险性评估管理_编辑")
    @BusinessLog(title = "危险性评估管理_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(RiskEvaluateManageParam.edit.class) RiskEvaluateManageParam riskEvaluateManageParam) {
        riskEvaluateManageService.edit(riskEvaluateManageParam);
        return new SuccessResponseData();
    }

    /**
     * 查看危险性评估管理
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    @Permission
    @GetMapping("/riskEvaluateManage/detail")
    @ApiOperation("危险性评估管理_查看")
    @BusinessLog(title = "危险性评估管理_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(RiskEvaluateManageParam.detail.class) RiskEvaluateManageParam riskEvaluateManageParam) {
        return new SuccessResponseData(riskEvaluateManageService.detail(riskEvaluateManageParam));
    }

    /**
     * 危险性评估管理列表
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    @Permission
    @GetMapping("/riskEvaluateManage/list")
    @ApiOperation("危险性评估管理_列表")
    @BusinessLog(title = "危险性评估管理_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(RiskEvaluateManageParam riskEvaluateManageParam) {
        return new SuccessResponseData(riskEvaluateManageService.list(riskEvaluateManageParam));
    }

    /**
     * 添加笔录-获取矫正对象所有已填写的笔录
     */
    @Permission
    @GetMapping("/riskEvaluateManage/getAllTranscript")
    @ApiOperation("危险性评估管理_获取矫正对象所有已填写的笔录")
    @BusinessLog(title = "危险性评估管理_获取矫正对象所有已填写的笔录", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getAllTranscript(@RequestParam String jzdxId) {
        return new SuccessResponseData(riskEvaluateManageService.getAllTranscript(jzdxId));
    }

    /**
     * 添加笔录
     *
     * @param id
     * @param transcriptId
     * @return
     */
    @Permission
    @GetMapping("/riskEvaluateManage/addTranscript")
    @ApiOperation("危险性评估管理_添加笔录")
    @BusinessLog(title = "危险性评估管理_添加笔录", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData addTranscript(@RequestParam String id, @RequestParam String transcriptId) {
        riskEvaluateManageService.addTranscript(id, transcriptId);
        return new SuccessResponseData();
    }

    /**
     * 添加模板
     *
     * @return
     */
    @Permission
    @GetMapping("/riskEvaluateManage/addTemplate")
    @ApiOperation("危险性评估管理_添加模板")
    @BusinessLog(title = "危险性评估管理_添加模板", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData addTemplate(@RequestParam String id, @RequestParam String templateId) {
        riskEvaluateManageService.addTemplate(id, templateId);
        return new SuccessResponseData();
    }

    /**
     * 当日入矫评估生成方法
     */
    @Permission
    @GetMapping("/riskEvaluateManage/generateEntryEvaluate")
    @ApiOperation("危险性评估管理_当日入矫评估生成方法")
    @BusinessLog(title = "危险性评估管理_当日入矫评估生成方法", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData generateEntryEvaluate() {
        riskEvaluateManageService.generateEntryEvaluate();
        return new SuccessResponseData();
    }

    /**
     * 根据评估类型生成评估数据
     */
    @Permission
    @GetMapping("/riskEvaluateManage/generateEvaluateByType")
    @ApiOperation("危险性评估管理_根据评估类型生成评估数据")
    @BusinessLog(title = "危险性评估管理_根据评估类型生成评估数据", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData generateEvaluateByType(@RequestParam String evaluateType, @RequestParam String jzdxId) {
        return new SuccessResponseData(riskEvaluateManageService.generateEvaluateByType(jzdxId, evaluateType));
    }

    /**
     * 根据评估信息生成笔录基本信息
     */
    @Permission
    @GetMapping("/riskEvaluateManage/generateBasicInfo")
    @ApiOperation("危险性评估管理_根据评估信息生成笔录基本信息")
    @BusinessLog(title = "危险性评估管理_根据评估信息生成笔录基本信息", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData generateBasicInfo(@RequestParam String id, @RequestParam String transcriptId) {
        return new SuccessResponseData(riskEvaluateManageService.generateBasicInfo(id, transcriptId));
    }

    /**
     * 提交笔录
     */
    @Permission
    @PostMapping("/riskEvaluateManage/submitTranscript")
    @ApiOperation("危险性评估管理_提交笔录")
    @BusinessLog(title = "危险性评估管理_提交笔录", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData submitTranscript(@RequestBody RiskTranscriptManagementParam riskTranscriptManagementParam) {
        riskEvaluateManageService.submitTranscript(riskTranscriptManagementParam);
        return new SuccessResponseData();
    }

    /**
     * 删除笔录
     */
    @Permission
    @PostMapping("/riskEvaluateManage/deleteTranscript")
    @ApiOperation("危险性评估管理_删除笔录")
    @BusinessLog(title = "危险性评估管理_删除笔录", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData deleteTranscript(@RequestBody RiskTranscriptManagementParam riskTranscriptManagementParam) {
        riskEvaluateManageService.deleteTranscript(riskTranscriptManagementParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑笔录
     */
    @Permission
    @PostMapping("/riskEvaluateManage/editTranscript")
    @ApiOperation("危险性评估管理_编辑笔录")
    @BusinessLog(title = "危险性评估管理_编辑笔录", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData editTranscript(@RequestBody RiskTranscriptManagementParam riskTranscriptManagementParam) {
        riskEvaluateManageService.editTranscript(riskTranscriptManagementParam);
        return new SuccessResponseData();
    }

    /**
     * 查看笔录
     */
    @Permission
    @GetMapping("/riskEvaluateManage/detailTranscript")
    @ApiOperation("危险性评估管理_查看笔录")
    @BusinessLog(title = "危险性评估管理_查看笔录", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData detailTranscript(@Validated(RiskTranscriptManagementParam.detail.class) RiskTranscriptManagementParam riskTranscriptManagementParam) {
        return new SuccessResponseData(riskEvaluateManageService.detailTranscript(riskTranscriptManagementParam));
    }

    /**
     * 下载pdf
     */
    @Permission
    @GetMapping("/riskEvaluateManage/downloadPdf")
    @ApiOperation("危险性评估管理_下载pdf")
    @BusinessLog(title = "危险性评估管理_下载pdf", opType = LogAnnotionOpTypeEnum.QUERY)
    public void downloadPdf(@RequestParam String id, HttpServletResponse response) {
        riskEvaluateManageService.downloadPdf(id, response);
    }

    /**
     * 下载word
     */
    @Permission
    @GetMapping("/riskEvaluateManage/downloadWord")
    @ApiOperation("危险性评估管理_下载word")
    @BusinessLog(title = "危险性评估管理_下载word", opType = LogAnnotionOpTypeEnum.QUERY)
    public void downloadWord(@RequestParam String id, HttpServletResponse response) {
        riskEvaluateManageService.downloadWord(id, response);
    }

    /**
     * 提交评估
     */
    @Permission
    @PostMapping("/riskEvaluateManage/submitEvaluate")
    @ApiOperation("危险性评估管理_提交评估")
    @BusinessLog(title = "危险性评估管理_提交评估", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData submitEvaluate(@RequestBody RiskEvaluateContext riskEvaluateContext) {
        riskEvaluateManageService.submitEvaluate(riskEvaluateContext);
        return new SuccessResponseData();
    }

    /**
     * 笔录PDF模板填充导出
     */
    @Permission
    @GetMapping("/riskEvaluateManage/fillPdfTemplate")
    @ApiOperation("危险性评估管理_笔录PDF模板填充导出")
    @BusinessLog(title = "危险性评估管理_笔录PDF模板填充导出", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void fillPdfTemplate(RiskTranscriptManagementParam riskTranscriptManagementParam, HttpServletResponse response) {
        riskEvaluateManageService.fillPdfTemplate(riskTranscriptManagementParam, response);
    }

    /**
     * 笔录Word模板填充导出
     */
    @Permission
    @GetMapping("/riskEvaluateManage/fillWordTemplate")
    @ApiOperation("危险性评估管理_笔录Word模板填充导出")
    @BusinessLog(title = "危险性评估管理_笔录Word模板填充导出", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void fillWordTemplate(RiskTranscriptManagementParam riskTranscriptManagementParam, HttpServletResponse response) {
        riskEvaluateManageService.fillWordTemplate(riskTranscriptManagementParam, response);
    }

    /**
     * 导出指标列表excel
     */
    @Permission
    @GetMapping("/riskEvaluateManage/exportIndexList")
    @ApiOperation("危险性评估管理_导出指标列表excel")
    @BusinessLog(title = "危险性评估管理_导出指标列表excel", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void exportIndexList(RiskEvaluateManageParam riskEvaluateManageParam, HttpServletResponse response) {
        riskEvaluateManageService.exportIndexList(riskEvaluateManageParam, response);
    }

    /**
     * 根据评估管理ID查询关联的量卷和表单详情
     */
    @Permission
    @GetMapping("/riskEvaluateManage/getEvaluationDetails")
    @ApiOperation("危险性评估管理_根据评估管理ID查询关联的量卷和表单详情")
    @BusinessLog(title = "危险性评估管理_根据评估管理ID查询关联的量卷和表单详情", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getEvaluationDetails(@RequestParam String evaluationManageId) {
        return new SuccessResponseData(riskEvaluateManageService.getEvaluationDetails(evaluationManageId));
    }

}
