package com.concise.gen.scoringmodeldetailmanage.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 评分模型详情
 *
 * <AUTHOR>
 * @date 2022-05-12 22:52:05
 */
@Data
@TableName("scoring_model_detail_manage")
public class ScoringModelDetailManage{

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 上级ID
     */
    private String topId;

    /**
     * 评分模型ID
     */
    private String scoringModelId;

    /**
     * 细则名称
     */
    private String detailName;

    /**
     * 指标分值
     */
    private Integer indexPoint;

    /**
     *存储 secondary_index_manage.id
     */
    private String extend01;

    /**
     *
     */
    private String extend02;

    /**
     *
     */
    private String extend03;

}
