package com.concise.gen.taskinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.node.AntdBaseTreeNode;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.taskinfo.entity.CorrectionSecConfig;
import com.concise.gen.taskinfo.entity.TaskInfoDetail;
import com.concise.gen.taskinfo.param.TaskInfoDetailParam;
import com.concise.gen.taskinfo.result.TaskInfoDetailResult;

import java.util.List;
import java.util.Set;

/**
 * 任务详情表service接口
 *
 * <AUTHOR>
 * @date 2022-02-28 15:17:32
 */
public interface TaskInfoDetailService extends IService<TaskInfoDetail> {

    /**
     * 查询任务详情表
     *
     * <AUTHOR>
     * @date 2022-02-28 15:17:32
     */
    PageResult<TaskInfoDetail> page(TaskInfoDetailParam taskInfoDetailParam, Set<String> org);

    /**
     * 任务详情表列表
     *
     * <AUTHOR>
     * @date 2022-02-28 15:17:32
     */
    List<TaskInfoDetail> list(TaskInfoDetailParam taskInfoDetailParam);

    /**
     * 添加任务详情表
     *
     * <AUTHOR>
     * @date 2022-02-28 15:17:32
     */
    void add(TaskInfoDetailParam taskInfoDetailParam);

    /**
     * 删除任务详情表
     *
     * <AUTHOR>
     * @date 2022-02-28 15:17:32
     */
    void delete(TaskInfoDetailParam taskInfoDetailParam);

    /**
     * 编辑任务详情表
     *
     * <AUTHOR>
     * @date 2022-02-28 15:17:32
     */
    void edit(TaskInfoDetailParam taskInfoDetailParam);


    /**
     * 查看任务详情表
     *
     * <AUTHOR>
     * @date 2022-02-28 15:17:32
     */
     TaskInfoDetail detail(TaskInfoDetailParam taskInfoDetailParam);

     TaskInfoDetailResult taskCount(TaskInfoDetailParam taskInfoDetailParam);

    void export(TaskInfoDetailParam taskInfoDetailParam, Set<String> org);

    /**
     * 获取所有重点时段节点
     * @return
     */
    List<AntdBaseTreeNode> keyPeriod();

    /**
     * 获取重点时段列表
     * @return
     */
    List<CorrectionSecConfig> getPointList();
}
