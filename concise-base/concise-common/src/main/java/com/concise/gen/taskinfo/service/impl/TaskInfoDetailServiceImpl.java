package com.concise.gen.taskinfo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.node.AntdBaseTreeNode;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.PoiUtil;
import com.concise.gen.taskinfo.entity.CorrectionSecConfig;
import com.concise.gen.taskinfo.entity.TaskInfoDetail;
import com.concise.gen.taskinfo.enums.TaskInfoDetailExceptionEnum;
import com.concise.gen.taskinfo.mapper.TaskInfoDetailMapper;
import com.concise.gen.taskinfo.param.TaskInfoDetailParam;
import com.concise.gen.taskinfo.result.TaskInfoDetailResult;
import com.concise.gen.taskinfo.service.TaskInfoDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * 任务详情表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-02-28 15:17:32
 */
@DS("dwhc")
@Service
public class TaskInfoDetailServiceImpl extends ServiceImpl<TaskInfoDetailMapper, TaskInfoDetail> implements TaskInfoDetailService {

    @Override
    public PageResult<TaskInfoDetail> page(TaskInfoDetailParam taskInfoDetailParam, Set<String> org) {
        QueryWrapper<TaskInfoDetail> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(taskInfoDetailParam)) {
            // 根据矫正机构id 查询
            queryWrapper.lambda().in(TaskInfoDetail::getJzjgId, org);
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getId())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getId, taskInfoDetailParam.getId());
            }
            // 根据任务信息ID 查询
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getTaskInfoId())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getTaskInfoId, taskInfoDetailParam.getTaskInfoId());
            }
            // 根据任务类型 查询
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getTaskType())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getTaskType, taskInfoDetailParam.getTaskType());
            }
            // 根据矫正人员姓名 查询
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getSqjzryName())) {
                queryWrapper.lambda().like(TaskInfoDetail::getSqjzryName, taskInfoDetailParam.getSqjzryName());
            }
            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(taskInfoDetailParam.getSearchBeginTime(), taskInfoDetailParam.getSearchEndTime())) {
                queryWrapper.lambda().ge(TaskInfoDetail::getStartTime, taskInfoDetailParam.getSearchBeginTime());
                queryWrapper.lambda().le(TaskInfoDetail::getStartTime, taskInfoDetailParam.getSearchEndTime());
            }
            // 根据外呼结果 查询
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getCallRet())) {
                if ("ybd".equals(taskInfoDetailParam.getCallRet())) {
                    //查询所有已拨打过的记录
                    queryWrapper.lambda().ne(TaskInfoDetail::getCallRet, "00");
                }else {
                    queryWrapper.lambda().eq(TaskInfoDetail::getCallRet, taskInfoDetailParam.getCallRet());
                }
            }
            // 根据声音校验结果 查询
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getAudioCheck())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getAudioCheck, taskInfoDetailParam.getAudioCheck());
            }
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getSync())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getSync, taskInfoDetailParam.getSync());
            }
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getWarningResult())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getWarningResult, taskInfoDetailParam.getWarningResult());
            }
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getWarningType())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getWarningType, taskInfoDetailParam.getWarningType());
            }
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getWarningExtendId())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getWarningExtendId, taskInfoDetailParam.getWarningExtendId());
            }
        }

        queryWrapper.lambda().orderByDesc(TaskInfoDetail::getStartTime);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<TaskInfoDetail> list(TaskInfoDetailParam taskInfoDetailParam) {
        return this.list();
    }

    @Override
    public void add(TaskInfoDetailParam taskInfoDetailParam) {
        TaskInfoDetail taskInfoDetail = new TaskInfoDetail();
        BeanUtil.copyProperties(taskInfoDetailParam, taskInfoDetail);
        this.save(taskInfoDetail);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(TaskInfoDetailParam taskInfoDetailParam) {
        this.removeById(taskInfoDetailParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(TaskInfoDetailParam taskInfoDetailParam) {
        TaskInfoDetail taskInfoDetail = this.queryTaskInfoDetail(taskInfoDetailParam);
        BeanUtil.copyProperties(taskInfoDetailParam, taskInfoDetail);
        this.updateById(taskInfoDetail);
    }



    @Override
    public TaskInfoDetail detail(TaskInfoDetailParam taskInfoDetailParam) {
        return this.queryTaskInfoDetail(taskInfoDetailParam);
    }

    /**
     * 获取任务详情表
     *
     * <AUTHOR>
     * @date 2022-02-28 15:17:32
     */
    private TaskInfoDetail queryTaskInfoDetail(TaskInfoDetailParam taskInfoDetailParam) {
        TaskInfoDetail taskInfoDetail = this.getById(taskInfoDetailParam.getId());
        if (ObjectUtil.isNull(taskInfoDetail)) {
            throw new ServiceException(TaskInfoDetailExceptionEnum.NOT_EXIST);
        }
        return taskInfoDetail;
    }

    @Override
    public TaskInfoDetailResult taskCount(TaskInfoDetailParam taskInfoDetailParam){
        QueryWrapper<TaskInfoDetailParam> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_info_id", taskInfoDetailParam.getTaskInfoId());
        return this.baseMapper.taskCount(queryWrapper);
    }

    @Override
    public void export(TaskInfoDetailParam taskInfoDetailParam, Set<String> org){
        QueryWrapper<TaskInfoDetail> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(taskInfoDetailParam)) {
            // 根据矫正机构id 查询
            queryWrapper.lambda().in(TaskInfoDetail::getJzjgId, org);
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getId())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getId, taskInfoDetailParam.getId());
            }
            // 根据任务信息ID 查询
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getTaskInfoId())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getTaskInfoId, taskInfoDetailParam.getTaskInfoId());
            }
            // 根据任务类型 查询
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getTaskType())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getTaskType, taskInfoDetailParam.getTaskType());
            }
            // 根据矫正人员姓名 查询
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getSqjzryName())) {
                queryWrapper.lambda().like(TaskInfoDetail::getSqjzryName, taskInfoDetailParam.getSqjzryName());
            }
            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(taskInfoDetailParam.getSearchBeginTime(), taskInfoDetailParam.getSearchEndTime())) {
                queryWrapper.lambda().ge(TaskInfoDetail::getStartTime, taskInfoDetailParam.getSearchBeginTime());
                queryWrapper.lambda().le(TaskInfoDetail::getStartTime, taskInfoDetailParam.getSearchEndTime());
            }
            // 根据外呼结果 查询
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getCallRet())) {
                if ("ybd".equals(taskInfoDetailParam.getCallRet())) {
                    //查询所有已拨打过的记录
                    queryWrapper.lambda().ne(TaskInfoDetail::getCallRet, "00");
                }else {
                    queryWrapper.lambda().eq(TaskInfoDetail::getCallRet, taskInfoDetailParam.getCallRet());
                }
            }
            // 根据声音校验结果 查询
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getAudioCheck())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getAudioCheck, taskInfoDetailParam.getAudioCheck());
            }
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getSync())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getSync, taskInfoDetailParam.getSync());
            }
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getWarningResult())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getWarningResult, taskInfoDetailParam.getWarningResult());
            }
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getWarningType())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getWarningType, taskInfoDetailParam.getWarningType());
            }
            if (ObjectUtil.isNotEmpty(taskInfoDetailParam.getWarningExtendId())) {
                queryWrapper.lambda().eq(TaskInfoDetail::getWarningExtendId, taskInfoDetailParam.getWarningExtendId());
            }
        }
        queryWrapper.lambda().orderByDesc(TaskInfoDetail::getStartTime);
        List<TaskInfoDetail> list = this.list(queryWrapper);
        PoiUtil.exportExcelWithStream("外呼记录.xls",TaskInfoDetail.class,list);
    }

    @Override
    public List<AntdBaseTreeNode> keyPeriod() {
        return this.baseMapper.getKeyPeriod();
    }

    @Override
    public List<CorrectionSecConfig> getPointList() {
        return this.baseMapper.getPointList();
    }
}
