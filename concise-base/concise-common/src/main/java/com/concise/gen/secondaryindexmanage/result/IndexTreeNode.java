package com.concise.gen.secondaryindexmanage.result;


import com.concise.common.pojo.base.node.BaseTreeNode;
import lombok.Data;
import java.util.List;

@Data
public class IndexTreeNode implements BaseTreeNode {
    /**
     * 二级指标ID
     */
    private String id;

    /**
     * 上级指标ID
     */
    private String topindexid;

    /**
     * 指标名称
     */
    private String indexName;

    /**
     * 排序
     */
    private String sort;

    /**
     * 指标分值
     */
    private String indexPoint;

    private List<IndexTreeNode> children;

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getPid() {
        return this.topindexid;
    }

    @Override
    public void setChildren(List children) {
        this.children = children;
    }
}
