package com.concise.gen.questioncategory.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 题库管理参数类
 *
 * <AUTHOR>
 * @date 2023-01-03 16:40:11
*/
@Data
public class QuestionCategoryParam extends BaseParam {

    /**
     * 唯一标识
     */
    @NotNull(message = "唯一标识不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 父分类id
     */
    @NotBlank(message = "父分类id不能为空，请检查parentId参数", groups = {add.class, edit.class})
    private String parentId;

    /**
     * 分类名
     */
    @NotBlank(message = "分类名不能为空，请检查typeName参数", groups = {add.class, edit.class})
    private String typeName;

    /**
     * 部门id
     */
    @NotBlank(message = "部门id不能为空，请检查depId参数", groups = {add.class, edit.class})
    private String depId;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空，请检查indexId参数", groups = {add.class, edit.class})
    private Integer indexId;

    /**
     * 分类等级
     */
    @NotBlank(message = "分类等级不能为空，请检查rank参数", groups = {add.class, edit.class})
    private String rank;

    /**
     * 是否删除
     */
    @NotBlank(message = "是否删除不能为空，请检查isDel参数", groups = {add.class, edit.class})
    private String isDel;

    /**
     * 
     */
    @NotBlank(message = "不能为空，请检查createBy参数", groups = {add.class, edit.class})
    private String createBy;

    /**
     * 
     */
    @NotBlank(message = "不能为空，请检查updateBy参数", groups = {add.class, edit.class})
    private String updateBy;

}
