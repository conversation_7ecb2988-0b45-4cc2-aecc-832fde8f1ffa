package com.concise.gen.scoringmodeldetailmanage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.scoringmodeldetailmanage.entity.ScoringModelDetailManage;
import com.concise.gen.scoringmodeldetailmanage.enums.ScoringModelDetailManageExceptionEnum;
import com.concise.gen.scoringmodeldetailmanage.mapper.ScoringModelDetailManageMapper;
import com.concise.gen.scoringmodeldetailmanage.param.ScoringModelDetailManageParam;
import com.concise.gen.scoringmodeldetailmanage.service.ScoringModelDetailManageService;
import com.concise.gen.scoringmodelmanage.entity.ScoringModelManage;
import com.concise.gen.scoringmodelmanage.service.ScoringModelManageService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 评分模型详情service接口实现类
 *
 * <AUTHOR>
 * @date 2022-05-12 22:52:05
 */
@Service
public class ScoringModelDetailManageServiceImpl extends ServiceImpl<ScoringModelDetailManageMapper, ScoringModelDetailManage> implements ScoringModelDetailManageService {

    @Resource
    private ScoringModelManageService scoringModelManageService;

    @Override
    public PageResult<ScoringModelDetailManage> page(ScoringModelDetailManageParam scoringModelDetailManageParam) {
        QueryWrapper<ScoringModelDetailManage> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(scoringModelDetailManageParam)) {

            // 根据上级ID 查询
            if (ObjectUtil.isNotEmpty(scoringModelDetailManageParam.getTopId())) {
                queryWrapper.lambda().eq(ScoringModelDetailManage::getTopId, scoringModelDetailManageParam.getTopId());
            }
            // 根据评分模型ID 查询
            if (ObjectUtil.isNotEmpty(scoringModelDetailManageParam.getScoringModelId())) {
                queryWrapper.lambda().eq(ScoringModelDetailManage::getScoringModelId, scoringModelDetailManageParam.getScoringModelId());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<ScoringModelDetailManage> list(ScoringModelDetailManageParam scoringModelDetailManageParam) {
        return this.list();
    }

    @Override
    public void add(ScoringModelDetailManageParam scoringModelDetailManageParam) {
        ScoringModelDetailManage scoringModelDetailManage = new ScoringModelDetailManage();
        BeanUtil.copyProperties(scoringModelDetailManageParam, scoringModelDetailManage);
        this.save(scoringModelDetailManage);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(ScoringModelDetailManageParam scoringModelDetailManageParam) {
        this.removeById(scoringModelDetailManageParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ScoringModelDetailManageParam scoringModelDetailManageParam) {
        ScoringModelDetailManage scoringModelDetailManage = this.queryScoringModelDetailManage(scoringModelDetailManageParam);
        BeanUtil.copyProperties(scoringModelDetailManageParam, scoringModelDetailManage);
        this.updateById(scoringModelDetailManage);
    }

    @Override
    public ScoringModelDetailManage detail(ScoringModelDetailManageParam scoringModelDetailManageParam) {
        return this.queryScoringModelDetailManage(scoringModelDetailManageParam);
    }

    /**
     * 获取评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    private ScoringModelDetailManage queryScoringModelDetailManage(ScoringModelDetailManageParam scoringModelDetailManageParam) {
        ScoringModelDetailManage scoringModelDetailManage = this.getById(scoringModelDetailManageParam.getId());
        if (ObjectUtil.isNull(scoringModelDetailManage)) {
            throw new ServiceException(ScoringModelDetailManageExceptionEnum.NOT_EXIST);
        }
        return scoringModelDetailManage;
    }

    @Override
    public String getModel(String timeStart) {
        QueryWrapper<ScoringModelManage> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ScoringModelManage::getIsEffect, 1);
        queryWrapper.lambda().le(ScoringModelManage::getEffectTimeStart, timeStart);
        List<ScoringModelManage> list = scoringModelManageService.list(queryWrapper);
        if (1 != list.size()) {
            // 如果符合评分条件的模型不止一个则直接返回0
            return null;
        }
        return list.get(0).getId();
    }

    @Override
    public int getPoint(String scoringModelId, String extend01) {
        QueryWrapper<ScoringModelDetailManage> dtlQueryWrapper = new QueryWrapper<>();
        dtlQueryWrapper.lambda().eq(ScoringModelDetailManage::getScoringModelId, scoringModelId);
        dtlQueryWrapper.lambda().eq(ScoringModelDetailManage::getExtend01, extend01);
        List<ScoringModelDetailManage> dtlList = this.list(dtlQueryWrapper);
        if (1 != dtlList.size()) {
            // 如果分值项不止一个则直接返回0
            return 0;
        }
        return dtlList.get(0).getIndexPoint();
    }
}
