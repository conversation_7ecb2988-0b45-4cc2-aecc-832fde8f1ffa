package com.concise.modular.llm.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.modular.llm.dto.BailianAnalyzeDTO;
import com.concise.modular.llm.service.BailianService;

/**
 * 百炼智能体控制器
 */
@RestController
@RequestMapping("/llm/bailian")
public class BailianController {

    @Resource
    private BailianService bailianService;

    /**
     * 分析矫正对象基本信息
     */
    @PostMapping("/analyze/basicInfo")
    public ResponseData analyzeBasicInfo(@RequestBody BailianAnalyzeDTO dto) {
//        String result = bailianService.analyzeBasicInfo(dto.getBasicInfo(), dto.getAgentCode(), dto.getAgentVersion());
        return new SuccessResponseData();
    }
}
