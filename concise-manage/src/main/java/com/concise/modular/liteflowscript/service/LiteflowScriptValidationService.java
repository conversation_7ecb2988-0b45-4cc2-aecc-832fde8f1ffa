package com.concise.modular.liteflowscript.service;

import cn.hutool.log.Log;
import com.concise.modular.liteflowscript.dto.ValidationResult;
import com.yomahub.liteflow.builder.el.LiteFlowChainELBuilder;
import com.yomahub.liteflow.enums.ScriptTypeEnum;
import com.yomahub.liteflow.script.validator.ScriptValidator;
import org.springframework.stereotype.Service;

import javax.tools.JavaCompiler;
import javax.tools.ToolProvider;
import java.io.StringWriter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * LiteFlow脚本校验服务
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Service
public class LiteflowScriptValidationService {

    private static final Log log = Log.get();

    /**
     * 校验Java脚本
     *
     * @param scriptData 脚本内容
     * @return 校验结果
     */
    public ValidationResult validateJavaScript(String scriptData) {
        try {
            if (scriptData == null || scriptData.trim().isEmpty()) {
                return ValidationResult.failure("脚本内容不能为空");
            }

            // 移除换行符进行基础校验
            String cleanScript = scriptData.replaceAll("\n", "");

            // 使用LiteFlow内置校验器
            boolean isValid = ScriptValidator.validate(cleanScript, ScriptTypeEnum.getEnumByDisplayName("java"));

            if (isValid) {
                return ValidationResult.success();
            } else {
                // 尝试进行更详细的语法检查
                return performDetailedValidation(scriptData);
            }

        } catch (Exception e) {
            log.error("脚本校验异常：{}", e.getMessage(), e);

            // 解析编译错误信息
            String errorMessage = parseCompilationError(e);
            return ValidationResult.failure("脚本校验失败", errorMessage);
        }
    }

    /**
     * 校验EL表达式
     *
     * @param elData EL表达式内容
     * @return 校验结果
     */
    public ValidationResult validateELExpression(String elData) {
        try {
            if (elData == null || elData.trim().isEmpty()) {
                return ValidationResult.failure("EL表达式不能为空");
            }

            // 移除换行符
            String cleanEl = elData.replaceAll("\n", "");

            // 使用LiteFlow EL校验器
            String validateResult = LiteFlowChainELBuilder.validateWithEx(cleanEl).toString();

            if (validateResult == null || validateResult.isEmpty() || "success".equals(validateResult)) {
                return ValidationResult.success();
            } else {
                return ValidationResult.failure("EL表达式语法错误", parseELError(validateResult));
            }

        } catch (Exception e) {
            log.error("EL表达式校验异常：{}", e.getMessage(), e);
            String errorMessage = parseELError(e.getMessage());
            return ValidationResult.failure("EL表达式校验失败", errorMessage);
        }
    }

    /**
     * 执行详细的脚本校验
     *
     * @param scriptData 脚本内容
     * @return 校验结果
     */
    private ValidationResult performDetailedValidation(String scriptData) {
        try {
            // 检查基本语法错误
            ValidationResult syntaxCheck = checkBasicSyntax(scriptData);
            if (!syntaxCheck.isValid()) {
                return syntaxCheck;
            }

            // 检查LiteFlow特定语法
            ValidationResult liteflowCheck = checkLiteFlowSyntax(scriptData);
            if (!liteflowCheck.isValid()) {
                return liteflowCheck;
            }

            return ValidationResult.failure("脚本语法错误", "请检查脚本语法是否正确");

        } catch (Exception e) {
            return ValidationResult.failure("详细校验失败", e.getMessage());
        }
    }

    /**
     * 检查基本Java语法
     *
     * @param scriptData 脚本内容
     * @return 校验结果
     */
    private ValidationResult checkBasicSyntax(String scriptData) {
        // 检查括号匹配
        if (!checkBracketMatching(scriptData)) {
            return ValidationResult.failure("语法错误", "括号不匹配");
        }

        // 检查分号
        if (!checkSemicolons(scriptData)) {
            return ValidationResult.failure("语法错误", "可能缺少分号");
        }

        // 检查字符串引号匹配
        if (!checkQuoteMatching(scriptData)) {
            return ValidationResult.failure("语法错误", "字符串引号不匹配");
        }

        return ValidationResult.success();
    }

    /**
     * 检查LiteFlow特定语法
     *
     * @param scriptData 脚本内容
     * @return 校验结果
     */
    private ValidationResult checkLiteFlowSyntax(String scriptData) {
        // 检查是否包含必要的LiteFlow方法
        if (!scriptData.contains("process") && !scriptData.contains("processSwitch") &&
            !scriptData.contains("processBoolean") && !scriptData.contains("processFor")) {
            return ValidationResult.failure("LiteFlow语法错误", "脚本必须包含process相关方法");
        }

        return ValidationResult.success();
    }

    /**
     * 检查括号匹配
     */
    private boolean checkBracketMatching(String script) {
        int roundBrackets = 0;
        int squareBrackets = 0;
        int curlyBrackets = 0;

        for (char c : script.toCharArray()) {
            switch (c) {
                case '(':
                    roundBrackets++;
                    break;
                case ')':
                    roundBrackets--;
                    if (roundBrackets < 0) return false;
                    break;
                case '[':
                    squareBrackets++;
                    break;
                case ']':
                    squareBrackets--;
                    if (squareBrackets < 0) return false;
                    break;
                case '{':
                    curlyBrackets++;
                    break;
                case '}':
                    curlyBrackets--;
                    if (curlyBrackets < 0) return false;
                    break;
            }
        }

        return roundBrackets == 0 && squareBrackets == 0 && curlyBrackets == 0;
    }

    /**
     * 检查分号（简单检查）
     */
    private boolean checkSemicolons(String script) {
        // 简单检查：如果有赋值语句但没有分号，可能有问题
        String[] lines = script.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (line.contains("=") && !line.contains("==") && !line.contains("!=") &&
                !line.contains("<=") && !line.contains(">=") &&
                !line.endsWith(";") && !line.endsWith("{") && !line.endsWith("}") &&
                !line.isEmpty() && !line.startsWith("//") && !line.startsWith("/*")) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查字符串引号匹配
     */
    private boolean checkQuoteMatching(String script) {
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;
        char prev = 0;

        for (char c : script.toCharArray()) {
            if (c == '\'' && prev != '\\' && !inDoubleQuote) {
                inSingleQuote = !inSingleQuote;
            } else if (c == '"' && prev != '\\' && !inSingleQuote) {
                inDoubleQuote = !inDoubleQuote;
            }
            prev = c;
        }

        return !inSingleQuote && !inDoubleQuote;
    }

    /**
     * 解析编译错误信息
     *
     * @param exception 异常对象
     * @return 格式化的错误信息
     */
    private String parseCompilationError(Exception exception) {
        String errorMessage = exception.getMessage();
        if (errorMessage == null) {
            return "未知编译错误";
        }

        // 解析DynamicCompilerException中的编译错误
        if (errorMessage.contains("DynamicCompilerException") && errorMessage.contains("Compilation Error:")) {
            return parseJavaCompilationError(errorMessage);
        }

        // 解析其他类型的错误
        if (errorMessage.contains("程序包") && errorMessage.contains("不存在")) {
            return parsePackageNotFoundError(errorMessage);
        }

        if (errorMessage.contains("找不到符号")) {
            return parseSymbolNotFoundError(errorMessage);
        }

        if (errorMessage.contains("语法错误")) {
            return parseSyntaxError(errorMessage);
        }

        // 返回原始错误信息的简化版本
        return simplifyErrorMessage(errorMessage);
    }

    /**
     * 解析Java编译错误
     */
    private String parseJavaCompilationError(String errorMessage) {
        StringBuilder result = new StringBuilder();
        String[] lines = errorMessage.split("\n");

        for (String line : lines) {
            line = line.trim();

            // 跳过空行和无关信息
            if (line.isEmpty() || line.contains("DynamicCompilerException") ||
                line.contains("Compilation Error:") || line.contains("/Execable$")) {
                continue;
            }

            // 解析错误行
            if (line.contains("错误:")) {
                String cleanError = extractErrorFromLine(line);
                if (!cleanError.isEmpty()) {
                    if (result.length() > 0) {
                        result.append("\n");
                    }
                    result.append(cleanError);
                }
            }
        }

        return result.length() > 0 ? result.toString() : "编译错误，请检查脚本语法";
    }

    /**
     * 从错误行中提取错误信息
     */
    private String extractErrorFromLine(String line) {
        // 提取行号信息
        String lineNumber = "";
        if (line.contains(".java:")) {
            int start = line.indexOf(".java:") + 6;
            int end = line.indexOf(":", start);
            if (end > start) {
                lineNumber = "第" + line.substring(start, end) + "行: ";
            }
        }

        // 提取错误描述
        if (line.contains("错误:")) {
            int errorStart = line.indexOf("错误:") + 3;
            String errorDesc = line.substring(errorStart).trim();
            return lineNumber + errorDesc;
        }

        return "";
    }

    /**
     * 解析包不存在错误
     */
    private String parsePackageNotFoundError(String errorMessage) {
        if (errorMessage.contains("程序包") && errorMessage.contains("不存在")) {
            // 提取包名
            String[] parts = errorMessage.split("程序包");
            if (parts.length > 1) {
                String packagePart = parts[1].split("不存在")[0].trim();
                return "导入错误: 程序包 " + packagePart + " 不存在，请检查包路径是否正确";
            }
        }
        return "包导入错误: " + errorMessage;
    }

    /**
     * 解析符号未找到错误
     */
    private String parseSymbolNotFoundError(String errorMessage) {
        return "符号错误: " + errorMessage;
    }

    /**
     * 解析语法错误
     */
    private String parseSyntaxError(String errorMessage) {
        return "语法错误: " + errorMessage;
    }

    /**
     * 简化错误信息
     */
    private String simplifyErrorMessage(String errorMessage) {
        // 移除堆栈跟踪信息，只保留主要错误信息
        String[] lines = errorMessage.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (!line.isEmpty() && !line.startsWith("at ") &&
                !line.contains("Exception") && !line.contains("Error")) {
                return line;
            }
        }

        // 如果没有找到合适的行，返回第一行非空内容
        for (String line : lines) {
            line = line.trim();
            if (!line.isEmpty()) {
                return line.length() > 200 ? line.substring(0, 200) + "..." : line;
            }
        }

        return "脚本校验失败，请检查语法";
    }

    /**
     * 解析EL表达式错误
     *
     * @param errorMessage 错误信息
     * @return 格式化的错误信息
     */
    private String parseELError(String errorMessage) {
        if (errorMessage == null || errorMessage.trim().isEmpty()) {
            return "EL表达式语法错误";
        }

        // 常见的EL错误模式
        if (errorMessage.contains("Unexpected token")) {
            return "语法错误: 意外的标记，请检查EL表达式语法";
        }

        if (errorMessage.contains("Missing")) {
            return "语法错误: 缺少必要的符号，请检查括号、逗号等是否完整";
        }

        if (errorMessage.contains("Invalid")) {
            return "语法错误: 无效的表达式，请检查EL语法是否正确";
        }

        if (errorMessage.contains("Expected")) {
            return "语法错误: 期望的符号不匹配，请检查表达式结构";
        }

        if (errorMessage.contains("component") && errorMessage.contains("not found")) {
            return "组件错误: 引用的组件不存在，请检查组件名称是否正确";
        }

        // 如果错误信息太长，截取主要部分
        if (errorMessage.length() > 200) {
            return errorMessage.substring(0, 200) + "...";
        }

        return errorMessage;
    }
}
