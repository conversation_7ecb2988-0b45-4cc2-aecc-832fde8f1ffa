package com.concise.modular.llm.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通义千问请求参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TongYiQianWenRequest {

    /**
     * 模型名称
     */
    private String model;

    /**
     * 对话消息列表
     */
    private List<Message> messages;

    /**
     * 是否流式输出
     */
    private Boolean stream;

    /**
     * API密钥
     */
    @JSONField(serialize = false)
    private String apiKey;
    
    /**
     * 最大生成令牌数
     */
    @JSONField(name = "max_tokens")
    private Integer maxTokens;
    
    /**
     * 温度参数
     */
    private Double temperature;
    
    /**
     * 核采样参数
     */
    @JSONField(name = "top_p")
    private Double topP;

    /**
     * 消息类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Message {
        /**
         * 角色：system, user, assistant
         */
        private String role;
        
        /**
         * 消息内容
         */
        private String content;
    }
}
