package com.concise.sys.modular.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.sys.modular.user.entity.SysUserRole;
import com.concise.sys.modular.user.param.SysUserParam;

import java.util.List;

/**
 * 系统用户角色service接口
 *
 * <AUTHOR>
 * @date 2020/3/13 15:44
 */
public interface SysUserRoleService extends IService<SysUserRole> {

    /**
     * 获取用户的角色id集合
     *
     * @param userId 用户id
     * @return 角色id集合
     * <AUTHOR>
     * @date 2020/3/20 22:29
     */
    List<String> getUserRoleIdList(String userId);

    /**
     * 授权角色
     *
     * @param sysUserParam 授权参数
     * <AUTHOR>
     * @date 2020/3/28 16:57
     */
    void grantRole(SysUserParam sysUserParam);

    /**
     * 获取用户所有角色的数据范围（组织机构id集合）
     *
     * @param userId 用户id
     * @param orgId  组织机构id
     * @return 数据范围id集合（组织机构id集合）
     * <AUTHOR>
     * @date 2020/4/5 17:31
     */
    List<String> getUserRoleDataScopeIdList(String userId, String orgId);

    /**
     * 根据角色id删除对应的用户-角色表关联信息
     *
     * @param roleId 角色id
     * <AUTHOR>
     * @date 2020/6/28 14:22
     */
    void deleteUserRoleListByRoleId(String roleId);

    /**
     * 根据用户id删除对应的用户-角色表关联信息
     *
     * @param userId 用户id
     * <AUTHOR>
     * @date 2020/6/28 14:52
     */
    void deleteUserRoleListByUserId(String userId);

    void insertDefaultRole(String idStr, String id);
}
