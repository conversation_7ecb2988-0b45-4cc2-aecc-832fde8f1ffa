package com.concise.modular.risk.riskevaluatemanage.service;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.modular.risk.riskevaluatemanage.dto.EvaluationDetailDto;
import com.concise.modular.risk.riskevaluatemanage.entity.RiskEvaluateContext;
import com.concise.modular.risk.riskevaluatemanage.entity.RiskEvaluateManage;
import com.concise.modular.risk.riskevaluatemanage.param.RiskEvaluateManageParam;
import com.concise.modular.risk.risktranscriptmanagement.entity.RiskTranscriptManagement;
import com.concise.modular.risk.risktranscriptmanagement.entity.TranscriptBasicInfo;
import com.concise.modular.risk.risktranscriptmanagement.param.RiskTranscriptManagementParam;

/**
 * 危险性评估管理service接口
 *
 * <AUTHOR>
 * @date 2025-07-14 15:58:13
 */
public interface RiskEvaluateManageService extends IService<RiskEvaluateManage> {

    /**
     * 查询危险性评估管理
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    PageResult<RiskEvaluateManage> page(RiskEvaluateManageParam riskEvaluateManageParam);

    /**
     * 危险性评估管理列表
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    List<RiskEvaluateManage> list(RiskEvaluateManageParam riskEvaluateManageParam);

    /**
     * 添加危险性评估管理
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    void add(RiskEvaluateManageParam riskEvaluateManageParam);

    /**
     * 删除危险性评估管理
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    void delete(RiskEvaluateManageParam riskEvaluateManageParam);

    /**
     * 编辑危险性评估管理
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    void edit(RiskEvaluateManageParam riskEvaluateManageParam);

    /**
     * 查看危险性评估管理
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    RiskEvaluateContext detail(RiskEvaluateManageParam riskEvaluateManageParam);

    /**
     * 根据矫正对象id初始化评估数据
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    RiskEvaluateManage initEvaluateManage(String id);

    /**
     * 获取矫正对象所有已填写的笔录
     *
     * <AUTHOR>
     * @date 2025-07-14 15:58:13
     */
    PageResult<RiskTranscriptManagement> getAllTranscript(String jzdxId);

    /**
     * 根据矫正对象信息生成评估信息
     */
    void generateEvaluateByPerson(CorrectionObjectInformation correctionObjectInformation, String evaluateType, RiskEvaluateContext riskEvaluateContext);


    /**
     * 当日入矫评估生成方法
     */
    void generateEntryEvaluate();

    /**
     * 发送风险评估浙政钉提醒通知
     *
     * @param riskEvaluateManage 风险评估管理对象
     */
    void sendRiskEvaluateZZDNotification(RiskEvaluateManage riskEvaluateManage);

    /**
     * 提交评估
     *
     * @param riskEvaluateContext
     */
    void submitEvaluate(RiskEvaluateContext riskEvaluateContext);


    /**
     * 提交笔录
     *
     * @param riskTranscriptManagementParam
     */
    void submitTranscript(RiskTranscriptManagementParam riskTranscriptManagementParam);

    /**
     * 查看笔录
     *
     * @param riskTranscriptManagementParam
     * @return
     */
    RiskTranscriptManagement detailTranscript(RiskTranscriptManagementParam riskTranscriptManagementParam);

    /**
     * 下载pdf
     *
     * @param id
     * @param response
     */
    void downloadPdf(String id, HttpServletResponse response);

    /**
     * 下载word
     *
     * @param id
     * @param response
     */
    void downloadWord(String id, HttpServletResponse response);

    /**
     * 删除笔录
     *
     * @param riskTranscriptManagementParam
     */
    void deleteTranscript(RiskTranscriptManagementParam riskTranscriptManagementParam);

    /**
     * 编辑笔录
     *
     * @param riskTranscriptManagementParam
     */
    void editTranscript(RiskTranscriptManagementParam riskTranscriptManagementParam);

    /**
     * 笔录pdf模板填充
     */
    void fillPdfTemplate(RiskTranscriptManagementParam riskTranscriptManagementParam, HttpServletResponse response);

    /**
     * 笔录word模板填充
     */
    void fillWordTemplate(RiskTranscriptManagementParam riskTranscriptManagementParam, HttpServletResponse response);

    /**
     * 添加笔录
     *
     * @param id
     * @param transcriptId
     */
    void addTranscript(String id, String transcriptId);

    /**
     * 添加模板
     *
     * @param id
     * @param templateId
     */
    void addTemplate(String id, String templateId);

    /**
     * 根据评估类型生成评估数据
     *
     * @param id           矫正对象id
     * @param evaluateType 评估类型
     */
    RiskEvaluateContext generateEvaluateByType(String id, String evaluateType);

    /**
     * 生成笔录基本信息
     *
     * @param id
     * @param transcriptId
     * @return
     */
    TranscriptBasicInfo generateBasicInfo(String id, String transcriptId);

    /**
     * 导出指标列表
     *
     * @param riskEvaluateManageParam
     * @param response
     */
    void exportIndexList(RiskEvaluateManageParam riskEvaluateManageParam, HttpServletResponse response);

    /**
     * 获取上一次的风险等级
     *
     * @param correctionObjectInformation
     * @return
     */
    String getLastRiskLevel(CorrectionObjectInformation correctionObjectInformation);

    /**
     * 根据评估管理ID查询关联的量卷和表单详情
     *
     * @param evaluationManageId 评估管理ID
     * @return 包含题目、用户选择的选项、得分等信息的列表
     */
    List<EvaluationDetailDto> getEvaluationDetails(String evaluationManageId);
}
