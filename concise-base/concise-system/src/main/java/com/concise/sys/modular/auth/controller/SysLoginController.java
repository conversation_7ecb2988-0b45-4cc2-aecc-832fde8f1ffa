package com.concise.sys.modular.auth.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.text.PasswdStrength;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSONObject;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.concise.common.context.constant.ConstantContextHolder;
import com.concise.common.exception.AuthException;
import com.concise.common.exception.enums.AuthExceptionEnum;
import com.concise.common.pojo.response.ErrorResponseData;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.common.util.MD5Util;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.core.login.SysLoginUser;
import com.concise.sys.modular.auth.service.AuthService;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.service.SysOrgService;
import com.concise.sys.modular.user.entity.SysUser;
import com.concise.sys.modular.user.service.SysUserDataScopeService;
import com.concise.sys.modular.user.service.SysUserService;
import com.concise.sys.modular.user.util.SM4Util;
import com.google.gson.Gson;
import com.pubinfo.client.util.JwtUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import sun.misc.BASE64Encoder;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 登录控制器
 *
 * <AUTHOR>
 * @date 2020/3/11 12:20
 */
@Api(tags = "登陆")
@Slf4j
@RestController
public class SysLoginController {

    @Resource
    private AuthService authService;

    @Lazy
    @Resource
    private CaptchaService captchaService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysUserDataScopeService sysUserDataScopeService;

    @Resource
    private SysOrgService sysOrgService;

    public static String zgpt_publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC7wZy0em4aRxiLFKleyELRM/AOf9BCe8PoR/H+sES2tN83ixF5oT2QGwyqwJh1RKWl7FMPCkElOoDfmhTT7P4N6AfR1gA5mVpZd2H0VDe/VrE42exYEtwTWpOL/NxgQzoOcYbQKt8XoX4JAq8Ym+diAkEwURG9SDsx+d6KdKggoQIDAQAB";
    public static String yj_publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCt9tTFN8/Ac3MQ354rU4JL9IZfknviG1gekvxxCcL/MboS16I3M5xRbBjxm2JQP/goTNXkKIWmpSYatd8RYkC+JHq3yagqy42qMBY2g0D1b/awWNuSNwFE3fWZ96BwUDEQx15kWmrchOlBJ4qkOdUaoJoP5l+TrGWIA4b8UU+jAwIDAQAB";


    /**
     * 获取是否开启租户的标识
     *
     * <AUTHOR>
     * @date 2020/9/4
     */
    @GetMapping("/getTenantOpen")
    public ResponseData getTenantOpen() {
        return new SuccessResponseData(ConstantContextHolder.getTenantOpenFlag());
    }

    /**
     * 账号密码登录
     *
     * <AUTHOR>
     * @date 2020/3/11 15:52
     */
    @PostMapping("/login")
    @ApiOperation("用户登录")
    public ResponseData login(@RequestBody Dict dict) {
        String type = dict.getStr("type");
        String account = dict.getStr("account");
        String password = SM4Util.decryptDatas(dict.getStr("password"), SM4Util.SECRET_KEY);
        if (null == password) {
            //解密失败则设置为原始密码
            log.info("login pass 解密失败：" + account + "***" + dict.getStr("password"));
            password = dict.getStr("password");
        }
        String tenantCode = dict.getStr("tenantCode");

        //检测是否开启验证码
        if (ConstantContextHolder.getCaptchaOpenFlag()) {
            verificationCode(dict.getStr("code"));
        }

        //如果系统开启了多租户开关，则添加租户的临时缓存
        if (ConstantContextHolder.getTenantOpenFlag()) {
            authService.cacheTenantInfo(tenantCode);
        }

        String token = authService.login(account, password, type);
        if ("sqjzry".equals(type)) {
            return new SuccessResponseData(JSONObject.parseObject(token));
        } else {
            int check = PasswdStrength.check(password);
            boolean passwordStrong = true;
            if (check <= 6) {
                passwordStrong = false;
                log.info("passwordStrong弱密码：" + account + "*" + password);
            }
            return new SuccessResponseData(token);
        }
    }

    /**
     * 退出登录
     *
     * <AUTHOR>
     * @date 2020/3/16 15:02
     */
    @GetMapping("/logout")
    public void logout() {
        authService.logout();
    }

    /**
     * 获取当前登录用户信息
     *
     * <AUTHOR>
     * @date 2020/3/23 17:57
     */
    @GetMapping("/getLoginUser")
    public ResponseData getLoginUser() {
        return new SuccessResponseData(LoginContextHolder.me().getSysLoginUserUpToDate());
    }

    /**
     * 获取验证码开关
     *
     * <AUTHOR>
     * @date 2021/1/21 15:27
     */
    @GetMapping("/getCaptchaOpen")
    public ResponseData getCaptchaOpen() {
        return new SuccessResponseData(ConstantContextHolder.getCaptchaOpenFlag());
    }

    /**
     * 校验验证码
     *
     * <AUTHOR>
     * @date 2021/1/21 15:27
     */
    private boolean verificationCode(String code) {
        CaptchaVO vo = new CaptchaVO();
        vo.setCaptchaVerification(code);
        if (!captchaService.verification(vo).isSuccess()) {
            throw new AuthException(AuthExceptionEnum.CONSTANT_EMPTY_ERROR);
        }
        return true;
    }

    /**
     * 账号密码登录（供集成大平台使用）
     *
     * <AUTHOR>
     * @date 2020/3/11 15:52
     */
    @PostMapping("/loginScreen")
    public ResponseData loginScreen(@RequestBody Dict dict) {
        String account = dict.getStr("username");
        String password = SM4Util.decryptDatas(dict.getStr("password"), SM4Util.SECRET_KEY);
        if (null == password) {
            //解密失败则设置为原始密码
            log.info("loginScreen pass 解密失败：" + account + "***" + dict.getStr("password"));
            password = dict.getStr("password");
        }
        String type = dict.getStr("type");
        SysLoginUser sysLoginUser = authService.loginScreen(account, password, type);
        int check = PasswdStrength.check(password);
        if (check <= 6 || password.indexOf("Tyyh") > -1) {
            //弱密码和 Tyyh 的 修改标记位，让前端弹出密码修改框
            sysLoginUser.setAvatar("0");
            log.info("loginScreen_passwordStrong弱密码：" + account + "*" + password);
        }
        return new SuccessResponseData(sysLoginUser);
    }

    @PostMapping("/loginByUsername")
    public ResponseData loginByUsername(@RequestBody Dict dict) {
        String account = dict.getStr("username");
        SysUser sysUser = sysUserService.getUserByCount(account);
        if (ObjectUtil.isEmpty(sysUser)) {
            return ResponseData.error("登录失败");
        }
        String token = authService.doLogin(sysUser);
        return ResponseData.success(token);
    }

    /**
     * 获取数据统计(供集成大平台使用)
     *
     * @return
     */
    @GetMapping("/getSjtj")
    @ApiOperation(value = "获取精准大平台数据统计", notes = "获取精准大平台数据统计")
    public ResponseData getSjtjResult() {
        JSONObject jsonObj = new JSONObject();
        String rlkTj = getDate("rlkTj");
        String zwkTj = getDate("zwkTj");
        String swkTj = getDate("swkTj");
        String sqjzdxTj = getDate("sqjzdxTj");
        String sfxzgzryTj = getDate("sfxzgzryTj");
        String shgzzTj = getDate("shgzzTj");
        String shzyzTj = getDate("shzyzTj");
        String rjxjTj = getDate("rjxjTj");
        String sqdcTj = getDate("sqdcTj");

        jsonObj.put("rlkTj", Integer.valueOf(rlkTj));
        jsonObj.put("zwkTj", Integer.valueOf(zwkTj));
        jsonObj.put("swkTj", Integer.valueOf(swkTj));
        jsonObj.put("sqjzdxTj", Integer.valueOf(sqjzdxTj));
        jsonObj.put("sfxzgzryTj", Integer.valueOf(sfxzgzryTj));
        jsonObj.put("shgzzTj", Integer.valueOf(shgzzTj));
        jsonObj.put("shzyzTj", Integer.valueOf(shzyzTj));
        jsonObj.put("rjxjTj", Integer.valueOf(rjxjTj));
        jsonObj.put("sqdcTj", Integer.valueOf(sqdcTj));
        return new SuccessResponseData(jsonObj);
    }

    /**
     * 根据类型获取数据统计的值(供集成大平台使用)
     *
     * @param type
     * @return
     */
    public String getDate(String type) {
        String url = "/message/sjTj?type=" + type;
        String res = res(url);
        JSONObject obj = JSONObject.parseObject(res);
        if (ObjectUtil.isNotNull(obj)) {
            String data = obj.getString("data");
            if (StringUtils.isNotEmpty(data)) {
                return data;
            }
        }
        return null;
    }

    /**
     * 根据url获取数据(供集成大平台使用)
     *
     * @param url
     * @return
     */
    public String res(String url) {
        HttpRequest http = new HttpRequest("http://10.249.0.85:8080" + url);
        http.header("Authorization", getToken("http://10.249.0.85:9012/oauth/token?grant_type=client_credentials", "xinxihua.platform", "Xxh2021@#!xxh"));
        http.header("Content-Type", "application/json");
        String res = http.method(Method.GET).execute().body();
        //log.info("res : {}", res);
        return res;
    }

    /**
     * 获取Token
     *
     * @return
     */
    public static String getToken(String tokenApi, String account, String passwd) {
        RestTemplate restTemplate = new RestTemplate();
        HashMap<String, String> paramMap = new HashMap<String, String>(16);
        //封装请求参数Params
        paramMap.put("grant_type", "client_credentials");
        String Authorization = "Basic enhqeTp6eGp5c3FqekAyMDIx";
        try {//Base64解码
            Authorization = new BASE64Encoder().encode((account + ":" + passwd).getBytes("utf-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        //获取请求体
        HttpEntity<String> requestEntity = getRequestEntity("Basic " + Authorization, paramMap);
        String result = restTemplate.postForObject(tokenApi, requestEntity, String.class);
        JSONObject json = JSONObject.parseObject(result);
        return json.getString("token_type") + " " + json.getString("access_token");
    }

    /**
     * 封装请求体
     *
     * @param Authorization
     * @param paramMap
     * @return
     */
    public static HttpEntity<String> getRequestEntity(String Authorization, HashMap<String, String> paramMap) {
        HttpHeaders headers = new HttpHeaders();
        //封装请求头
        headers.add("Authorization", Authorization);
        headers.setContentType(MediaType.APPLICATION_JSON);
        //转换JSON
        String json = new Gson().toJson(paramMap);
        HttpEntity<String> httpEntity = new HttpEntity<String>(json, headers);
        return httpEntity;
    }

    @ApiOperation(value = "综管平台单点登陆", notes = "综管平台单点登陆")
    @GetMapping(value = "/sys/zgSsoLogin")
    public ResponseData zgSsoLogin(@RequestParam String username) throws Exception {
        SysUser sysUser = sysUserService.getUserByCount(username);
        String userId = sysUser.getId();
        String id_token = (new Date()).getTime() + "#" + userId;
        String id_token_zgpt_en = encrypt(id_token, zgpt_publicKey);
        System.out.println("id_token_zgpt_en: " + URLEncoder.encode(id_token_zgpt_en, "UTF-8"));
        String url = "https://sqjz.zjsft.gov.cn/ssologin/login?id_token=" + URLEncoder.encode(id_token_zgpt_en, "UTF-8");
        return new SuccessResponseData(url);
    }

    @ApiOperation(value = "预警平台单点登陆", notes = "预警平台单点登陆")
    @GetMapping(value = "/sys/yjSsoLogin")
    public ResponseData yjSsoLogin(@RequestParam String username) throws Exception {
        SysUser sysUser = sysUserService.getUserByCount(username);
        String userId = sysUser.getId();
        String id_token = (new Date()).getTime() + "#" + userId;
        String id_token_yj_en = encrypt(id_token, yj_publicKey);
        String url = "http://59.202.53.104:4000/ssologin/login?id_token=" + URLEncoder.encode(id_token_yj_en, "UTF-8");
        return new SuccessResponseData(url);
    }

    /**
     * RSA公钥加密
     *
     * @param str       加密字符串
     * @param publicKey 公钥
     * @return 密文
     * @throws Exception 加密过程中的异常信息
     */
    public static String encrypt(String str, String publicKey) throws Exception {
        //base64编码的公钥
        byte[] decoded = org.apache.commons.codec.binary.Base64.decodeBase64(publicKey);
        RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
        //RSA加密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);
        String outStr = org.apache.commons.codec.binary.Base64.encodeBase64String(cipher.doFinal(str.getBytes("UTF-8")));
        return outStr;
    }

    // 集成大平台单点登录
    @PostMapping("/sso/login")
    public ResponseData getRYLogin(@RequestBody SysUser sysUser) {
        SysUser sysUsers = sysUserService.getUserByCount(SM4Util.decryptDatas(sysUser.getAccount(), SM4Util.SECRET_KEY));
        if (ObjectUtil.isEmpty(sysUsers)) {
            return ResponseData.error("用户不存在");
        }
        Map map = new HashMap();
        map.put("password", SM4Util.encryptDatas(sysUsers.getPassword(), SM4Util.SECRET_KEY));
        map.put("username", sysUser.getAccount());
        return new SuccessResponseData(map);
    }

    @ApiOperation(value = "单点登录接口之灵犀_浙里连心", notes = "单点登录接口之灵犀_浙里连心")
    @GetMapping(value = "/sso/login/sjAdminLogin")
    public ResponseData sjAdminLogin(@RequestParam("username") String username, @RequestParam("deptId") String deptId) {
        String urlPrefix = "http://59.202.53.104:10002/sso/login/sjAdminLogin";
        String id = username;
        String key = "fnCDOaGwK1M9sXF0";
        //不传就是省级， 地市对应dishiId， 区县对应quxianId，  司法所传quxianId jiedaoId
        String dishiId = "";
        String quxianId = "";
        String jiedaoId = "";
        //如果是部门的则，往上找父级机构
        SysOrg org = sysOrgService.getById(deptId);
        if ("sp".equals(org.getType())) {
            org = sysOrgService.getById(org.getPid());
        }
        if (null != org) {
            if ("city".equals(org.getDescription())) {
                //地市
                dishiId = org.getId();
            }
            if (StringUtils.isEmpty(org.getDescription())) {
                //区县
                quxianId = org.getId();
            } else if ("sfs".equals(org.getDescription())) {
                jiedaoId = org.getId();
                //司法所  往上找区县一级
                org = sysOrgService.getById(org.getPid());
                quxianId = org.getId();
            }
        } else {
            quxianId = deptId;
        }
        long timestamp = System.currentTimeMillis();
        String params = "id=" + id + "&timestamp=" + timestamp + "&dishiId=" + dishiId + "&quxianId=" + quxianId + "&jiedaoId=" + jiedaoId + "&key=" + key;
        String sign = MD5Util.toMD5(params).toUpperCase();
        String url = urlPrefix + "?id=" + id + "&sign=" + sign + "&timestamp=" + timestamp + "&dishiId=" + dishiId + "&quxianId=" + quxianId + "&jiedaoId=" + jiedaoId;
        return new SuccessResponseData(url);
    }

    @ApiOperation(value = "单点登录接口之灵犀_浙里连心", notes = "单点登录接口之灵犀_浙里连心")
    @GetMapping(value = "/sso/login/getParams")
    public ResponseData getParams(@RequestParam("username") String username, @RequestParam("deptId") String deptId) {
        //String urlPrefix = "http://59.202.53.104:10002/sso/login/sjAdminLogin";
        String id = username;
        String key = "fnCDOaGwK1M9sXF0";
        //不传就是省级， 地市对应dishiId， 区县对应quxianId，  司法所传quxianId jiedaoId
        String dishiId = "";
        String quxianId = "";
        String jiedaoId = "";
        //如果是部门的则，往上找父级机构
        SysOrg org = sysOrgService.getById(deptId);
        if ("sp".equals(org.getType())) {
            org = sysOrgService.getById(org.getPid());
        }

        //部分机构需要传上级机构
        SysOrg pOrg = sysOrgService.zllxFilter(deptId);
        if (pOrg != null) {
            org = pOrg;
        }

        if (null != org) {
            if ("city".equals(org.getDescription())) {
                //地市
                dishiId = org.getId();
            }
            if (StringUtils.isEmpty(org.getDescription())) {
                //区县
                quxianId = org.getId();
            } else if ("sfs".equals(org.getDescription())) {
                jiedaoId = org.getId();
                //司法所  往上找区县一级
                org = sysOrgService.getById(org.getPid());
                quxianId = org.getId();
            }
        } else {
            quxianId = deptId;
        }
        long timestamp = System.currentTimeMillis();
        String params = "id=" + id + "&timestamp=" + timestamp + "&dishiId=" + dishiId + "&quxianId=" + quxianId + "&jiedaoId=" + jiedaoId + "&key=" + key;
        String sign = MD5Util.toMD5(params).toUpperCase();
        String result = "&id=" + id + "&sign=" + sign + "&timestamp=" + timestamp + "&dishiId=" + dishiId + "&quxianId=" + quxianId + "&jiedaoId=" + jiedaoId;
        return new SuccessResponseData(result);
    }


    /**
     * 之矫汇
     *
     * @param dict
     * @return
     */

    @ApiOperation(value = "之矫汇单点登录", notes = "之矫汇单点登录")
    @PostMapping(value = "/zjhLogin")
    public ResponseData zjhLogin(@RequestBody Dict dict) {
        SysUser sysUser = sysUserService.getUserByCount(dict.getStr("username"));
        SysOrg org = sysOrgService.getById(dict.getStr("departIds"));
        List<String> deptIds = sysUserDataScopeService.getUserDataScopeIdList(sysUser.getId());
        if (ObjectUtil.isNotNull(sysUser) && ObjectUtil.isNotNull(org) && CollectionUtil.isNotEmpty(deptIds)) {
            if (deptIds.stream().anyMatch(id -> id.equals(org.getId()))) {

                SysLoginUser sysLoginUser = authService.loginScreen(sysUser.getAccount(), sysUser.getPassword(), null);
                return new SuccessResponseData(sysLoginUser.getToken());
            }

        }
        return new ErrorResponseData(1, "登录失败，用户不存在！");
    }

    @ApiOperation("单点登录接口（龙晰新综管）")
    @PostMapping(value = "/ssologinLx")
    public ResponseData ssologinLx(@RequestParam String sToken, HttpServletRequest request) {
        log.info("=====ossLoginLx:s_token====" + sToken);
        String rsObj = JwtUtil.parseToken("qwiqkuja", sToken);
        log.info("=====ossLoginLx:s_token解析rsObj====" + rsObj);
        if (ObjectUtil.isEmpty(rsObj)) {
            // 若解析出的值为null，请检查token是否过期，token有效期默认为10分钟
            return ResponseData.error("token异常，登录失败！");
        }
        JSONObject obj = JSONObject.parseObject(rsObj);
        String employeeCode = obj.getString("userId");
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(SysUser::getZzdId, employeeCode);
        List<SysUser> userList = sysUserService.list(queryWrapper);
        if (userList.size() > 0 && null != userList.get(0)) {
            Map map = new HashMap();
            map.put("password", SM4Util.encryptDatas(userList.get(0).getPassword(), SM4Util.SECRET_KEY));
            map.put("username", userList.get(0).getAccount());
            return new SuccessResponseData(map);
        } else {
            return ResponseData.error("用户不存在！");
        }
    }


}
