package com.concise.gen.scoringmodeldetailmanage. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.scoringmodeldetailmanage. param.ScoringModelDetailManageParam;
import com.concise.gen.scoringmodeldetailmanage. service.ScoringModelDetailManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 评分模型详情控制器
 *
 * <AUTHOR>
 * @date 2022-05-12 22:52:05
 */
@Api(tags = "评分模型详情")
@RestController
public class ScoringModelDetailManageController {

    @Resource
    private ScoringModelDetailManageService scoringModelDetailManageService;

    /**
     * 查询评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    @Permission
    @GetMapping("/scoringModelDetailManage/page")
    @ApiOperation("评分模型详情_分页查询")
    @BusinessLog(title = "评分模型详情_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(ScoringModelDetailManageParam scoringModelDetailManageParam) {
        return new SuccessResponseData(scoringModelDetailManageService.page(scoringModelDetailManageParam));
    }

    /**
     * 添加评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    @Permission
    @PostMapping("/scoringModelDetailManage/add")
    @ApiOperation("评分模型详情_增加")
    @BusinessLog(title = "评分模型详情_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(ScoringModelDetailManageParam.add.class) ScoringModelDetailManageParam scoringModelDetailManageParam) {
        scoringModelDetailManageService.add(scoringModelDetailManageParam);
        return new SuccessResponseData();
    }

    /**
     * 删除评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    @Permission
    @PostMapping("/scoringModelDetailManage/delete")
    @ApiOperation("评分模型详情_删除")
    @BusinessLog(title = "评分模型详情_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(ScoringModelDetailManageParam.delete.class) ScoringModelDetailManageParam scoringModelDetailManageParam) {
        scoringModelDetailManageService.delete(scoringModelDetailManageParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    @Permission
    @PostMapping("/scoringModelDetailManage/edit")
    @ApiOperation("评分模型详情_编辑")
    @BusinessLog(title = "评分模型详情_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(ScoringModelDetailManageParam.edit.class) ScoringModelDetailManageParam scoringModelDetailManageParam) {
        scoringModelDetailManageService.edit(scoringModelDetailManageParam);
        return new SuccessResponseData();
    }

    /**
     * 查看评分模型详情
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    @Permission
    @GetMapping("/scoringModelDetailManage/detail")
    @ApiOperation("评分模型详情_查看")
    @BusinessLog(title = "评分模型详情_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(ScoringModelDetailManageParam.detail.class) ScoringModelDetailManageParam scoringModelDetailManageParam) {
        return new SuccessResponseData(scoringModelDetailManageService.detail(scoringModelDetailManageParam));
    }

    /**
     * 评分模型详情列表
     *
     * <AUTHOR>
     * @date 2022-05-12 22:52:05
     */
    @Permission
    @GetMapping("/scoringModelDetailManage/list")
    @ApiOperation("评分模型详情_列表")
    @BusinessLog(title = "评分模型详情_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(ScoringModelDetailManageParam scoringModelDetailManageParam) {
        return new SuccessResponseData(scoringModelDetailManageService.list(scoringModelDetailManageParam));
    }

}
