package com.concise.gen.portraitImage.correctionscalebase.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.concise.gen.portraitImage.correctionquestion.entity.CorrectionQuestion;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.List;

/**
 * 量表配置基本信息表
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_scale_base")
public class CorrectionScaleBase extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 量表名称
     */
    private String title;

    /**
     * 启用情况（0：启用 1：停用）
     */
    private Integer status;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

    /**
     * 问题列表
     */
    @TableField(exist = false)
    private List<CorrectionQuestion> questionList;

}
