package com.concise.gen.secondaryindexmanage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.factory.TreeBuildFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.secondaryindexmanage.entity.SecondaryIndexManage;
import com.concise.gen.secondaryindexmanage.enums.SecondaryIndexManageExceptionEnum;
import com.concise.gen.secondaryindexmanage.mapper.SecondaryIndexManageMapper;
import com.concise.gen.secondaryindexmanage.param.SecondaryIndexManageParam;
import com.concise.gen.secondaryindexmanage.result.IndexTreeNode;
import com.concise.gen.secondaryindexmanage.service.SecondaryIndexManageService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 二级指标管理信息表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:21
 */
@Service
public class SecondaryIndexManageServiceImpl extends ServiceImpl<SecondaryIndexManageMapper, SecondaryIndexManage> implements SecondaryIndexManageService {

    @Override
    public PageResult<SecondaryIndexManage> page(SecondaryIndexManageParam secondaryIndexManageParam) {
        QueryWrapper<SecondaryIndexManage> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(secondaryIndexManageParam)) {

            // 根据上级指标ID 查询
            if (ObjectUtil.isNotEmpty(secondaryIndexManageParam.getTopindexid())) {
                queryWrapper.lambda().eq(SecondaryIndexManage::getTopindexid, secondaryIndexManageParam.getTopindexid());
            }
            // 根据指标名称 查询
            if (ObjectUtil.isNotEmpty(secondaryIndexManageParam.getIndexName())) {
                queryWrapper.lambda().like(SecondaryIndexManage::getIndexName, secondaryIndexManageParam.getIndexName());
            }
            // 根据删除状态 查询
            if (ObjectUtil.isNotEmpty(secondaryIndexManageParam.getDelFlag())) {
                queryWrapper.lambda().eq(SecondaryIndexManage::getDelFlag, secondaryIndexManageParam.getDelFlag());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<SecondaryIndexManage> list(SecondaryIndexManageParam secondaryIndexManageParam) {
        QueryWrapper<SecondaryIndexManage> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(secondaryIndexManageParam)) {

            // 根据上级指标ID 查询
            if (ObjectUtil.isNotEmpty(secondaryIndexManageParam.getTopindexid())) {
                queryWrapper.lambda().eq(SecondaryIndexManage::getTopindexid, secondaryIndexManageParam.getTopindexid());
            }
            // 根据指标名称 查询
            if (ObjectUtil.isNotEmpty(secondaryIndexManageParam.getIndexName())) {
                queryWrapper.lambda().like(SecondaryIndexManage::getIndexName, secondaryIndexManageParam.getIndexName());
            }

        }
        queryWrapper.lambda().eq(SecondaryIndexManage::getDelFlag, "0");
        return this.list(queryWrapper);
    }

    @Override
    public void add(SecondaryIndexManageParam secondaryIndexManageParam) {
        SecondaryIndexManage secondaryIndexManage = new SecondaryIndexManage();
        BeanUtil.copyProperties(secondaryIndexManageParam, secondaryIndexManage);
        this.save(secondaryIndexManage);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(SecondaryIndexManageParam secondaryIndexManageParam) {
        SecondaryIndexManage secondaryIndexManage = new SecondaryIndexManage();
        secondaryIndexManage.setId(secondaryIndexManageParam.getId());
        secondaryIndexManage.setDelFlag("1");
        this.updateById(secondaryIndexManage);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(SecondaryIndexManageParam secondaryIndexManageParam) {
        SecondaryIndexManage secondaryIndexManage = this.querySecondaryIndexManage(secondaryIndexManageParam);
        BeanUtil.copyProperties(secondaryIndexManageParam, secondaryIndexManage);
        this.updateById(secondaryIndexManage);
    }

    @Override
    public SecondaryIndexManage detail(SecondaryIndexManageParam secondaryIndexManageParam) {
        return this.querySecondaryIndexManage(secondaryIndexManageParam);
    }

    @Override
    public List<IndexTreeNode> tree(SecondaryIndexManageParam secondaryIndexManageParam) {
        secondaryIndexManageParam.setDelFlag("0");
        List<IndexTreeNode> resultList = CollectionUtil.newArrayList();
        QueryWrapper<SecondaryIndexManage> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SecondaryIndexManage::getExtend01, secondaryIndexManageParam.getTopindexid());
        queryWrapper.lambda().eq(SecondaryIndexManage::getDelFlag, secondaryIndexManageParam.getDelFlag());
        queryWrapper.lambda().orderByAsc(SecondaryIndexManage::getSort);
        this.list(queryWrapper).forEach(secondaryIndexManage -> {
            IndexTreeNode node = new IndexTreeNode();
            BeanUtil.copyProperties(secondaryIndexManage, node);
            resultList.add(node);
        });

        return new TreeBuildFactory<IndexTreeNode>().doTreeBuild(resultList);
    }

    /**
     * 获取二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    private SecondaryIndexManage querySecondaryIndexManage(SecondaryIndexManageParam secondaryIndexManageParam) {
        SecondaryIndexManage secondaryIndexManage = this.getById(secondaryIndexManageParam.getId());
        if (ObjectUtil.isNull(secondaryIndexManage)) {
            throw new ServiceException(SecondaryIndexManageExceptionEnum.NOT_EXIST);
        }
        return secondaryIndexManage;
    }
}
