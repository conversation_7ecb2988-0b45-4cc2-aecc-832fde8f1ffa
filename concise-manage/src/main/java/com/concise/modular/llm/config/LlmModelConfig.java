package com.concise.modular.llm.config;

import com.concise.modular.llm.entity.LlmConfig;
import lombok.Data;

/**
 * LLM模型配置类
 */
@Data
public class LlmModelConfig {
    /**
     * 模型名称
     */
    private String name;
    
    /**
     * 模型显示名称
     */
    private String displayName;
    
    /**
     * API密钥
     */
    private String apiKey;
    
    /**
     * API接口地址
     */
    private String apiUrl;
    
    /**
     * 默认最大令牌数
     */
    private Integer maxTokens = 2048;
    
    /**
     * 默认温度参数
     */
    private Double temperature = 0.7;
    
    /**
     * 默认top_p参数
     */
    private Double topP = 0.8;

    /**
     * 从数据库配置创建模型配置
     */
    public static LlmModelConfig fromDbConfig(LlmConfig config) {
        if (config == null) {
            return null;
        }
        LlmModelConfig modelConfig = new LlmModelConfig();
        modelConfig.setName(config.getModelName());
        modelConfig.setDisplayName(config.getDisplayName());
        modelConfig.setApiKey(config.getApiKey());
        modelConfig.setApiUrl(config.getApiUrl());
        modelConfig.setMaxTokens(config.getMaxTokens());
        modelConfig.setTemperature(config.getTemperature());
        modelConfig.setTopP(config.getTopP());
        return modelConfig;
    }
}
