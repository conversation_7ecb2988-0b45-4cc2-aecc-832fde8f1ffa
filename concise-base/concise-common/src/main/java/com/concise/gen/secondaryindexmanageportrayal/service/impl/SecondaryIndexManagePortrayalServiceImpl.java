package com.concise.gen.secondaryindexmanageportrayal.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.factory.TreeBuildFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.secondaryindexmanage.enums.SecondaryIndexManageExceptionEnum;
import com.concise.gen.secondaryindexmanageportrayal.entity.SecondaryIndexManagePortrayal;
import com.concise.gen.secondaryindexmanageportrayal.mapper.SecondaryIndexManagePortrayalMapper;
import com.concise.gen.secondaryindexmanageportrayal.param.SecondaryIndexManagePortrayalParam;
import com.concise.gen.secondaryindexmanageportrayal.result.IndexTreeNode;
import com.concise.gen.secondaryindexmanageportrayal.service.SecondaryIndexManagePortrayalService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 二级指标管理信息表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:21
 */
@Service
public class SecondaryIndexManagePortrayalServiceImpl extends ServiceImpl<SecondaryIndexManagePortrayalMapper, SecondaryIndexManagePortrayal> implements SecondaryIndexManagePortrayalService {

    @Override
    public PageResult<SecondaryIndexManagePortrayal> page(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        QueryWrapper<SecondaryIndexManagePortrayal> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(secondaryIndexManagePortrayalParam)) {

            // 根据上级指标ID 查询
            if (ObjectUtil.isNotEmpty(secondaryIndexManagePortrayalParam.getTopindexid())) {
                queryWrapper.lambda().eq(SecondaryIndexManagePortrayal::getTopindexid, secondaryIndexManagePortrayalParam.getTopindexid());
            }
            // 根据指标名称 查询
            if (ObjectUtil.isNotEmpty(secondaryIndexManagePortrayalParam.getIndexName())) {
                queryWrapper.lambda().like(SecondaryIndexManagePortrayal::getIndexName, secondaryIndexManagePortrayalParam.getIndexName());
            }
            // 根据删除状态 查询
            if (ObjectUtil.isNotEmpty(secondaryIndexManagePortrayalParam.getDelFlag())) {
                queryWrapper.lambda().eq(SecondaryIndexManagePortrayal::getDelFlag, secondaryIndexManagePortrayalParam.getDelFlag());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<SecondaryIndexManagePortrayal> list(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        QueryWrapper<SecondaryIndexManagePortrayal> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(secondaryIndexManagePortrayalParam)) {

            // 根据上级指标ID 查询
            if (ObjectUtil.isNotEmpty(secondaryIndexManagePortrayalParam.getTopindexid())) {
                queryWrapper.lambda().eq(SecondaryIndexManagePortrayal::getTopindexid, secondaryIndexManagePortrayalParam.getTopindexid());
            }
            // 根据指标名称 查询
            if (ObjectUtil.isNotEmpty(secondaryIndexManagePortrayalParam.getIndexName())) {
                queryWrapper.lambda().like(SecondaryIndexManagePortrayal::getIndexName, secondaryIndexManagePortrayalParam.getIndexName());
            }

        }
        queryWrapper.lambda().eq(SecondaryIndexManagePortrayal::getDelFlag, "0");
        return this.list(queryWrapper);
    }

    @Override
    public void add(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        SecondaryIndexManagePortrayal secondaryIndexManagePortrayal = new SecondaryIndexManagePortrayal();
        BeanUtil.copyProperties(secondaryIndexManagePortrayalParam, secondaryIndexManagePortrayal);
        this.save(secondaryIndexManagePortrayal);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        SecondaryIndexManagePortrayal secondaryIndexManagePortrayal = new SecondaryIndexManagePortrayal();
        secondaryIndexManagePortrayal.setId(secondaryIndexManagePortrayalParam.getId());
        secondaryIndexManagePortrayal.setDelFlag("1");
        this.updateById(secondaryIndexManagePortrayal);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        SecondaryIndexManagePortrayal secondaryIndexManagePortrayal = this.querySecondaryIndexManage(secondaryIndexManagePortrayalParam);
        BeanUtil.copyProperties(secondaryIndexManagePortrayalParam, secondaryIndexManagePortrayal);
        this.updateById(secondaryIndexManagePortrayal);
    }

    @Override
    public SecondaryIndexManagePortrayal detail(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        return this.querySecondaryIndexManage(secondaryIndexManagePortrayalParam);
    }

    @Override
    public List<IndexTreeNode> tree(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        secondaryIndexManagePortrayalParam.setDelFlag("0");
        List<IndexTreeNode> resultList = CollectionUtil.newArrayList();
        QueryWrapper<SecondaryIndexManagePortrayal> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SecondaryIndexManagePortrayal::getExtend01, secondaryIndexManagePortrayalParam.getTopindexid());
        queryWrapper.lambda().eq(SecondaryIndexManagePortrayal::getDelFlag, secondaryIndexManagePortrayalParam.getDelFlag());
        queryWrapper.lambda().orderByAsc(SecondaryIndexManagePortrayal::getSort);
        this.list(queryWrapper).forEach(secondaryIndexManage -> {
            IndexTreeNode node = new IndexTreeNode();
            BeanUtil.copyProperties(secondaryIndexManage, node);
            resultList.add(node);
        });

        return new TreeBuildFactory<IndexTreeNode>().doTreeBuild(resultList);
    }

    /**
     * 获取二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    private SecondaryIndexManagePortrayal querySecondaryIndexManage(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam) {
        SecondaryIndexManagePortrayal secondaryIndexManagePortrayal = this.getById(secondaryIndexManagePortrayalParam.getId());
        if (ObjectUtil.isNull(secondaryIndexManagePortrayal)) {
            throw new ServiceException(SecondaryIndexManageExceptionEnum.NOT_EXIST);
        }
        return secondaryIndexManagePortrayal;
    }
}
