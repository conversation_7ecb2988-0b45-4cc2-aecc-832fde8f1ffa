package com.concise.sys.modular.dict.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.sys.modular.dict.entity.SysDictData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统字典值mapper接口
 *
 * <AUTHOR>
 * @date 2020/3/13 16:12
 */
public interface SysDictDataMapper extends BaseMapper<SysDictData> {

    /**
     * 通过字典类型code获取字典编码值列表
     *
     * @param dictTypeCodes 字典类型编码集合
     * @return 字典编码值列表
     * <AUTHOR>
     * @date 2020/8/9 14:27
     */
    List<String> getDictCodesByDictTypeCode(String[] dictTypeCodes);

    /**
     * 查找字典值
     * @param value
     * @param remark
     * @return
     */
    List<SysDictData> findList(@Param("value")String value, @Param("remark")String remark);

    /**
     * 根据code获取字典值
     * @param code
     * @return
     */
    SysDictData getByCode(String code);

    /**
     * 根据字典类型code获取字典值
     * @param typeCode
     * @return
     */
    List<SysDictData> listByType(@Param("typeCode") String typeCode);
}
