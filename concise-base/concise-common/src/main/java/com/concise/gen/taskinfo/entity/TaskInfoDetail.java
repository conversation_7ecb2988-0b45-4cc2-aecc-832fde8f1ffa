package com.concise.gen.taskinfo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.pojo.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 任务详情表
 *
 * <AUTHOR>
 * @date 2022-02-28 15:17:32
 */
@DS("dwhc")
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("task_info_detail")
public class TaskInfoDetail extends BaseEntity {

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 任务信息ID
     */
    private String taskInfoId;

    /**
     * 任务类型
     */
    @Excel(name = "任务类型", replace = {"日常监管_01", "预警核实_02", "电话报到_03"}, width = 20,orderNum = "8")
    private String taskType;
    private String taskSource;

    /**
     * 通话话术ID
     */
    private String dictionId;

    /**
     * 通话标题
     */
    @Excel(name = "话术", width = 20,orderNum = "3")
    private String dictionTitle;

    /**
     * 矫正机构ID
     */
    private String jzjgId;

    /**
     * 矫正机构名称
     */
    @Excel(name = "矫正单位", width = 30,orderNum = "1")
    private String jzjgName;

    /**
     * 矫正人员姓名
     */
    @Excel(name = "姓名", width = 20,orderNum = "0")
    private String sqjzryName;
    /**
     * 社区矫正人员id
     */
    private String sqjzryId;

    /**
     * 矫正人员个人联系电话
     */
    @Excel(name = "联系电话", width = 20,orderNum = "2")
    private String sqjzryTel;

    /**
     * 呼叫时间
     */
    @Excel(name = "呼叫时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20,orderNum = "4")
    private Date startTime;

    /**
     * 挂机时间
     */
    private Date endTime;

    /**
     * 全程通话音频http地址
     */
    private String audio;

    /**
     * 交互记录
     */
    private String interact;

    /**
     * 外呼结果
     */
    @Excel(name = "外呼结果", replace = {"待外呼_00", "未接通_02", "已接通_03", "已重播_04"}, width = 20,orderNum = "5")
    private String callRet;


    /**
     * 声音校验结果
     */
    @Excel(name = "声音校验结果", replace = {"通过_Y", "未通过_N", "_null"}, width = 20,orderNum = "7")
    private String audioCheck;
    /**
     * 通话服务时长
     */
    private int svcTime;
    /**
     * 同步状态
     */
    private String sync;
    /**
     * 本月设定电话报告频次
     */
    private int frequency;
    /**
     * 监管等级
     */
    private String jzjbName;
    private String warningType;
    private String warningResult;
    private Date warningTime;
    /**
     * 云雀平台对应记录id
     */
    private String warningExtendId;

    /**
     * 外呼记录id
     */
    private String callId;
    /**
     * 振铃时间
     */
    private Date ringTime;
    /**
     * 振铃时长
     */
    private int ringDuration;
    /**
     * 外呼任务关联项（移动）
     */
    private String taskName;
}
