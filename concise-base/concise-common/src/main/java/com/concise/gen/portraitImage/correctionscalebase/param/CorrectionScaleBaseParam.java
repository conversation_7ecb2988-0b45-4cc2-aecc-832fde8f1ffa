package com.concise.gen.portraitImage.correctionscalebase.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.portraitImage.correctionquestion.entity.CorrectionQuestion;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
* 量表配置基本信息表参数类
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:21
*/
@Data
public class CorrectionScaleBaseParam extends BaseParam {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 量表名称
     */
    @ApiModelProperty(value = "量表名称")
    private String title;

    /**
     * 启用情况（0：启用 1：停用）
     */
    @ApiModelProperty(value = "启用情况（0：启用 1：停用）")
    private Integer status;

    /**
     * 是否删除（0：未删除，1删除）
     */
    @ApiModelProperty(value = "是否删除（0：未删除，1删除）")
    private Integer delFlag;

    /**
     * 问题列表
     */
    @ApiModelProperty(value = "问题列表")
    private List<CorrectionQuestion> questionList;
}
