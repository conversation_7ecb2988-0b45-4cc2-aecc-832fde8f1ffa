package com.concise.modular.applysource;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.concise.common.pojo.response.ResponseData;
import com.concise.modular.label.correctionlabeltable.service.CorrectionLabelTableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(tags = "数据源管理")
@RestController
@RequestMapping("/api/datasource")
public class DataSourceController {

    @Resource
    private DynamicRoutingDataSource dynamicDataSource;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private CorrectionLabelTableService correctionLabelTableService;

    // 获取所有数据源
    @ApiOperation("获取所有数据源")
    @GetMapping("/list")
    public ResponseData getDataSources(@RequestParam(required = false) String dataSourceId) {
        Map<String, DataSource> dataSources = dynamicDataSource.getCurrentDataSources();
        List<Map<String, String>> result = new ArrayList<>();

        for (Map.Entry<String, DataSource> entry : dataSources.entrySet()) {
            Map<String, String> dsInfo = new HashMap<>();
            String dataSourceValue = entry.getKey();
            dsInfo.put("dataSourceValue", dataSourceValue);
            if ("ccgf".equals(dataSourceValue)) {
                dsInfo.put("name", "云雀");
            } else if ("slave".equals(dataSourceValue)) {
                dsInfo.put("name", "数据中心");
            } else if ("dwhc".equals(dataSourceValue)) {
                dsInfo.put("name", "电话核查");
            } else if ("master".equals(dataSourceValue)) {
                dsInfo.put("name", "精准矫正");
            } else {
                dsInfo.put("name", dataSourceValue);
            }
            result.add(dsInfo);
        }

        return ResponseData.success(result);
    }

    // 获取指定数据源的所有表
    @ApiOperation("获取指定数据源的所有表")
    @GetMapping("/tables")
    public ResponseData getTables(@RequestParam String dataSourceId) {
//        DynamicDataSourceContextHolder.push(dataSourceId);
//        try {
//            List<Map<String, Object>> tables = jdbcTemplate.queryForList(
//                    "SELECT table_name as tableName, table_comment as tableComment " +
//                            "FROM information_schema.tables " +
//                            "WHERE table_schema = DATABASE() AND table_type = 'BASE TABLE'");
//            // 过滤掉以"sys_"开头的表
//            tables.removeIf(table -> table.get("tableName").toString().startsWith("sys_"));
//            return ResponseData.success(tables);
//        } finally {
//            DynamicDataSourceContextHolder.poll();
//        }
        return ResponseData.success();
    }

    // 获取表结构
    @ApiOperation("获取指定数据源的表结构")
    @GetMapping("/schema")
    public ResponseData getTableSchema(@RequestParam String dataSourceId,
                                       @RequestParam String tableName) {
//        DynamicDataSourceContextHolder.push(dataSourceId);
//        try {
//            List<TableColumn> columns = jdbcTemplate.query(
//                    "SELECT column_name, data_type, column_comment FROM information_schema.columns " +
//                            "WHERE table_schema = DATABASE() AND table_name = ?",
//                    (rs, rowNum) -> new TableColumn(
//                            rs.getString("column_name"),
//                            rs.getString("data_type"),
//                            rs.getString("column_comment")
//                    ),
//                    tableName
//            );
//            return ResponseData.success(columns);
//        } finally {
//            DynamicDataSourceContextHolder.poll();
//        }
        return ResponseData.success();
    }

    /**
     * 生成并保存表数据
     */
    @ApiOperation("生成并保存表数据")
    @GetMapping("/generate")
    public ResponseData generateTableData(@RequestParam String dataSourceId,
                                          @RequestParam String tableName) {
        correctionLabelTableService.addTableAndColumns(dataSourceId, tableName);
        return ResponseData.success();
    }

}
