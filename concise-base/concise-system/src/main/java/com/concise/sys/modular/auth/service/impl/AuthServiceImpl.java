package com.concise.sys.modular.auth.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.concise.common.consts.CacheKeyConstant;
import com.concise.common.consts.CommonConstant;
import com.concise.common.context.constant.ConstantContextHolder;
import com.concise.common.enums.CommonStatusEnum;
import com.concise.common.exception.AuthException;
import com.concise.common.exception.ServiceException;
import com.concise.common.exception.enums.AuthExceptionEnum;
import com.concise.common.exception.enums.ServerExceptionEnum;
import com.concise.common.util.HttpServletUtil;
import com.concise.common.util.IpAddressUtil;
import com.concise.core.dbs.CurrentDataSourceContext;
import com.concise.core.login.LoginEmpInfo;
import com.concise.core.login.SysLoginUser;
import com.concise.core.tenant.context.TenantCodeHolder;
import com.concise.core.tenant.context.TenantDbNameHolder;
import com.concise.core.tenant.entity.TenantInfo;
import com.concise.core.tenant.exception.TenantException;
import com.concise.core.tenant.exception.enums.TenantExceptionEnum;
import com.concise.core.tenant.service.TenantInfoService;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.correctionobjectinformation.mapper.CorrectionObjectInformationMapper;
import com.concise.sys.core.cache.UserCache;
import com.concise.sys.core.enums.LogSuccessStatusEnum;
import com.concise.sys.core.jwt.JwtPayLoad;
import com.concise.sys.core.jwt.JwtTokenUtil;
import com.concise.sys.core.log.LogManager;
import com.concise.sys.modular.auth.factory.LoginUserFactory;
import com.concise.sys.modular.auth.service.AuthService;
import com.concise.sys.modular.user.entity.SysUser;
import com.concise.sys.modular.user.service.SysUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 认证相关service实现类
 *
 * <AUTHOR>
 * @date 2020/3/11 16:58
 */
@Service
public class AuthServiceImpl implements AuthService, UserDetailsService {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private UserCache userCache;

    @Resource
    private CorrectionObjectInformationMapper correctionObjectInformationMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public String login(String account, String password, String type) {

        if ("sqjzry".equals(type)) {
            //矫正对象
            List<CorrectionObjectInformation> list = correctionObjectInformationMapper.getBySfzh(account);
            if (list.size() == 0 || ObjectUtil.isEmpty(list.get(0).getSfzhMw())) {
                LogManager.me().executeLoginLog(account, LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_PWD_ERROR.getMessage());
                throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR);
            }
            String sfzh = list.get(0).getSfzhMw();
            String disabledKey = CacheKeyConstant.SYS_USER_DISABLED_LIST + account;
            if (stringRedisTemplate.hasKey(disabledKey)) {
                throw new AuthException(AuthExceptionEnum.ACCOUNT_FREEZE_ERROR);
            }

            //密码校验： 默认密码：Sqjz@ + 身份证后6位
            String pass = "Sqjz@" + sfzh.substring(sfzh.length() - 6, sfzh.length());
            if(!pass.equals(password)) {
                LogManager.me().executeLoginLog(account, LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_PWD_ERROR.getMessage());
                // throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR);
                String key = CacheKeyConstant.SYS_USER_LOGIN_ERROR_COUNT + account;
                int count = 1;
                if (stringRedisTemplate.hasKey(key)) {
                    count = Convert.toInt(stringRedisTemplate.opsForValue().get(key)) + 1;
                }
                if (count == CommonConstant.LOGIN_ERROR_COUNT) {
                    stringRedisTemplate.opsForValue().set(disabledKey,
                            count + "", CacheKeyConstant.SYS_USER_DISABLED_LIST_TIME, TimeUnit.SECONDS);
                }
                stringRedisTemplate.opsForValue().set(key, count + "", CacheKeyConstant.SYS_USER_LOGIN_ERROR_COUNT_TIME, TimeUnit.SECONDS);

                // AuthExceptionEnum authExceptionEnum = new AuthExceptionEnum("", "");

                throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR.getCode(),
                        "账号或密码错误，请检查account或password参数连续5次登录失败,将会锁定账号,当前错误" + count + "次");
            }
            return doLoginSqjzry(list.get(0));
        } else {
            //工作人员
            if (ObjectUtil.hasEmpty(account, password)) {
                LogManager.me().executeLoginLog(account, LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_PWD_EMPTY.getMessage());
                throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_EMPTY);
            }

            String disabledKey = CacheKeyConstant.SYS_USER_DISABLED_LIST + account;
            if (stringRedisTemplate.hasKey(disabledKey)) {
                throw new AuthException(AuthExceptionEnum.ACCOUNT_FREEZE_ERROR);
            }

            SysUser sysUser = sysUserService.getUserByCount(account);

            //用户不存在，账号或密码错误
            if (ObjectUtil.isEmpty(sysUser)) {
                LogManager.me().executeLoginLog(account, LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_PWD_ERROR.getMessage());
                throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR);
            }

            String passwordBcrypt = sysUser.getPassword();
            //弃用默认加密，采用md5
            String md5PasswordBcrypt = DigestUtil.md5Hex(password);

            if(ObjectUtil.isEmpty(type)) {
                //验证账号密码是否正确
                if (ObjectUtil.isEmpty(passwordBcrypt) || !md5PasswordBcrypt.equals(passwordBcrypt)) {
                    LogManager.me().executeLoginLog(sysUser.getAccount(), LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_PWD_ERROR.getMessage());
                    //throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR);

                    String key = CacheKeyConstant.SYS_USER_LOGIN_ERROR_COUNT + sysUser.getAccount();
                    int count = 1;
                    if (stringRedisTemplate.hasKey(key)) {
                        count = Convert.toInt(stringRedisTemplate.opsForValue().get(key)) + 1;
                    }
                    if (count == CommonConstant.LOGIN_ERROR_COUNT) {
                        stringRedisTemplate.opsForValue().set(disabledKey,
                                count + "", CacheKeyConstant.SYS_USER_DISABLED_LIST_TIME, TimeUnit.SECONDS);
                    }
                    stringRedisTemplate.opsForValue().set(key, count + "", CacheKeyConstant.SYS_USER_LOGIN_ERROR_COUNT_TIME, TimeUnit.SECONDS);

                    // AuthExceptionEnum authExceptionEnum = new AuthExceptionEnum("", "");

                    throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR.getCode(),
                            "账号或密码错误，请检查account或password参数连续5次登录失败,将会锁定账号,当前错误" + count + "次");
                }
            }

            return doLogin(sysUser);
        }
    }

    public String doLoginSqjzry(CorrectionObjectInformation sqjzry) {

        //构造SysLoginUser
        SysLoginUser sysLoginUser = new SysLoginUser();
        sysLoginUser.setId(sqjzry.getId());
        sysLoginUser.setAccount(sqjzry.getSfzhMw());
        sysLoginUser.setName(sqjzry.getXm());
        LoginEmpInfo emp = new LoginEmpInfo();
        emp.setOrgId(sqjzry.getJzjg());
        emp.setOrgName(sqjzry.getJzjgName());
        sysLoginUser.setLoginEmpInfo(emp);

        //构造jwtPayLoad
        JwtPayLoad jwtPayLoad = new JwtPayLoad(sqjzry.getId(), sqjzry.getSfzhMw());

        //生成token
        String token = JwtTokenUtil.generateToken(jwtPayLoad);

        //缓存token与登录用户信息对应, 默认2个小时
        this.cacheLoginUser(jwtPayLoad, sysLoginUser);

        //登录成功，设置SpringSecurityContext上下文，方便获取用户
        this.setSpringSecurityContextAuthentication(sysLoginUser);

        //返回token
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("token", token);
        jsonObject.put("userInfo", sysLoginUser);
        return jsonObject.toJSONString();
    }

    @Override
    public String doLogin(SysUser sysUser) {

        Integer sysUserStatus = sysUser.getStatus();

        //验证账号是否被冻结
        if (CommonStatusEnum.DISABLE.getCode().equals(sysUserStatus)) {
            LogManager.me().executeLoginLog(sysUser.getAccount(), LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_FREEZE_ERROR.getMessage());
            throw new AuthException(AuthExceptionEnum.ACCOUNT_FREEZE_ERROR);
        }

        //构造SysLoginUser
        SysLoginUser sysLoginUser = this.genSysLoginUser(sysUser);

        //构造jwtPayLoad
        JwtPayLoad jwtPayLoad = new JwtPayLoad(sysUser.getId(), sysUser.getAccount());

        //生成token
        String token = JwtTokenUtil.generateToken(jwtPayLoad);

        //缓存token与登录用户信息对应, 默认2个小时
        this.cacheLoginUser(jwtPayLoad, sysLoginUser);

        //设置最后登录ip和时间
        sysUser.setLastLoginIp(IpAddressUtil.getIp(HttpServletUtil.getRequest()));
        sysUser.setLastLoginTime(DateTime.now());

        //更新用户登录信息
        sysUserService.updateById(sysUser);

        //登录成功，记录登录日志
        LogManager.me().executeLoginLog(sysUser.getAccount(), LogSuccessStatusEnum.SUCCESS.getCode(), null);

        //登录成功，设置SpringSecurityContext上下文，方便获取用户
        this.setSpringSecurityContextAuthentication(sysLoginUser);

        //如果开启限制单用户登陆，则踢掉原来的用户
        Boolean enableSingleLogin = ConstantContextHolder.getEnableSingleLogin();
        if (enableSingleLogin) {

            //获取所有的登陆用户
            Map<String, SysLoginUser> allLoginUsers = userCache.getAllKeyValues();
            for (Map.Entry<String, SysLoginUser> loginedUserEntry : allLoginUsers.entrySet()) {

                String loginedUserKey = loginedUserEntry.getKey();
                SysLoginUser loginedUser = loginedUserEntry.getValue();

                //如果账号名称相同，并且redis缓存key和刚刚生成的用户的uuid不一样，则清除以前登录的
                if (loginedUser.getName().equals(sysUser.getName())
                        && !loginedUserKey.equals(jwtPayLoad.getUuid())) {
                    this.clearUser(loginedUserKey, loginedUser.getAccount());
                }
            }
        }

        //返回token
        return token;
    }

    @Override
    public String getTokenFromRequest(HttpServletRequest request) {
        String authToken = request.getHeader(CommonConstant.AUTHORIZATION);
        if (ObjectUtil.isEmpty(authToken) || CommonConstant.UNDEFINED.equals(authToken)) {
            return null;
        } else {
            //token不是以Bearer打头，则响应回格式不正确
            if (!authToken.startsWith(CommonConstant.TOKEN_TYPE_BEARER)) {
                throw new AuthException(AuthExceptionEnum.NOT_VALID_TOKEN_TYPE);
            }
            try {
                authToken = authToken.substring(CommonConstant.TOKEN_TYPE_BEARER.length() + 1);
            } catch (StringIndexOutOfBoundsException e) {
                throw new AuthException(AuthExceptionEnum.NOT_VALID_TOKEN_TYPE);
            }
        }

        return authToken;
    }

    @Override
    public SysLoginUser getLoginUserByToken(String token) {

        //校验token，错误则抛异常
        this.checkToken(token);

        //根据token获取JwtPayLoad部分
        JwtPayLoad jwtPayLoad = JwtTokenUtil.getJwtPayLoad(token);

        //从redis缓存中获取登录用户
        Object cacheObject = userCache.get(jwtPayLoad.getUuid());

        //用户不存在则表示登录已过期
        if (ObjectUtil.isEmpty(cacheObject)) {
            throw new AuthException(AuthExceptionEnum.LOGIN_EXPIRED);
        }

        //转换成登录用户
        SysLoginUser sysLoginUser = (SysLoginUser) cacheObject;

        //用户存在, 无痛刷新缓存，在登录过期前活动的用户自动刷新缓存时间
        this.cacheLoginUser(jwtPayLoad, sysLoginUser);

        //返回用户
        return sysLoginUser;
    }

    @Override
    public void logout() {

        HttpServletRequest request = HttpServletUtil.getRequest();

        if (ObjectUtil.isNotNull(request)) {

            //获取token
            String token = this.getTokenFromRequest(request);

            //如果token为空直接返回
            if (ObjectUtil.isEmpty(token)) {
                return;
            }

            //校验token，错误则抛异常，待确定
            this.checkToken(token);

            //根据token获取JwtPayLoad部分
            JwtPayLoad jwtPayLoad = JwtTokenUtil.getJwtPayLoad(token);

            //获取缓存的key
            String loginUserCacheKey = jwtPayLoad.getUuid();
            this.clearUser(loginUserCacheKey, jwtPayLoad.getAccount());

        } else {
            throw new ServiceException(ServerExceptionEnum.REQUEST_EMPTY);
        }
    }

    @Override
    public void setSpringSecurityContextAuthentication(SysLoginUser sysLoginUser) {
        UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken =
                new UsernamePasswordAuthenticationToken(
                        sysLoginUser,
                        null,
                        sysLoginUser.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
    }

    @Override
    public Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    @Override
    public void checkToken(String token) {
        //校验token是否正确
        Boolean tokenCorrect = JwtTokenUtil.checkToken(token);
        if (!tokenCorrect) {
            throw new AuthException(AuthExceptionEnum.REQUEST_TOKEN_ERROR);
        }

        //校验token是否失效
        Boolean tokenExpired = JwtTokenUtil.isTokenExpired(token);
        if (tokenExpired) {
            throw new AuthException(AuthExceptionEnum.LOGIN_EXPIRED);
        }
    }

    @Override
    public void cacheTenantInfo(String tenantCode) {
        if (StrUtil.isBlank(tenantCode)) {
            return;
        }

        // 从spring容器中获取service，如果没开多租户功能，没引入相关包，这里会报错
        TenantInfoService tenantInfoService = null;
        try {
            tenantInfoService = SpringUtil.getBean(TenantInfoService.class);
        } catch (Exception e) {
            throw new TenantException(TenantExceptionEnum.TENANT_MODULE_NOT_ENABLE_ERROR);
        }

        // 获取租户信息
        TenantInfo tenantInfo = tenantInfoService.getByCode(tenantCode);
        if (tenantInfo != null) {
            String dbName = tenantInfo.getDbName();

            // 租户编码的临时存放
            TenantCodeHolder.put(tenantCode);

            // 租户的数据库名称临时缓存
            TenantDbNameHolder.put(dbName);

            // 数据源信息临时缓存
            CurrentDataSourceContext.setDataSourceType(dbName);
        } else {
            throw new TenantException(TenantExceptionEnum.CNAT_FIND_TENANT_ERROR);
        }
    }

    @Override
    public SysLoginUser loadUserByUsername(String account) throws UsernameNotFoundException {
        SysLoginUser sysLoginUser = new SysLoginUser();
        SysUser user = sysUserService.getUserByCount(account);
        BeanUtil.copyProperties(user, sysLoginUser);
        return sysLoginUser;
    }

    /**
     * 根据key清空登陆信息
     *
     * <AUTHOR>
     * @date 2020/6/19 12:28
     */
    private void clearUser(String loginUserKey, String account) {
        //获取缓存的用户
        Object cacheObject = userCache.get(loginUserKey);

        //如果缓存的用户存在，清除会话，否则表示该会话信息已失效，不执行任何操作
        if (ObjectUtil.isNotEmpty(cacheObject)) {
            //清除登录会话
            userCache.remove(loginUserKey);
            //创建退出登录日志
            LogManager.me().executeExitLog(account);
        }
    }

    /**
     * 构造登录用户信息
     *
     * <AUTHOR>
     * @date 2020/3/12 17:32
     */
    @Override
    public SysLoginUser genSysLoginUser(SysUser sysUser) {
        SysLoginUser sysLoginUser = new SysLoginUser();
        BeanUtil.copyProperties(sysUser, sysLoginUser);
        LoginUserFactory.fillLoginUserInfo(sysLoginUser);
        return sysLoginUser;
    }

    /**
     * 缓存token与登录用户信息对应, 默认2个小时
     *
     * <AUTHOR>
     * @date 2020/3/13 14:51
     */
    private void cacheLoginUser(JwtPayLoad jwtPayLoad, SysLoginUser sysLoginUser) {
        String redisLoginUserKey = jwtPayLoad.getUuid();
        userCache.put(redisLoginUserKey, sysLoginUser, Convert.toLong(ConstantContextHolder.getSessionTokenExpireSec()));
    }

    @Override
    public SysLoginUser loginScreen(String account, String password, String type) {
        if (ObjectUtil.hasEmpty(account, password)) {
            LogManager.me().executeLoginLog(account, LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_PWD_EMPTY.getMessage());
            throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_EMPTY);
        }

        String disabledKey = CacheKeyConstant.SYS_USER_DISABLED_LIST + account;
        if (stringRedisTemplate.hasKey(disabledKey)) {
            throw new AuthException(AuthExceptionEnum.ACCOUNT_FREEZE_ERROR);
        }

        SysUser sysUser = sysUserService.getUserByCount(account);

        //用户不存在，账号或密码错误
        if (ObjectUtil.isEmpty(sysUser)) {
            LogManager.me().executeLoginLog(account, LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_PWD_ERROR.getMessage());
            throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR);
        }

        String passwordBcrypt = sysUser.getPassword();
        //弃用默认加密，采用md5
        String md5PasswordBcrypt = DigestUtil.md5Hex(password);
        if ("sso".equals(type)) {
            // type = sso 则password已经是md5格式的密码，无需再转
            md5PasswordBcrypt = password;
        }

        //验证账号密码是否正确
        if (ObjectUtil.isEmpty(passwordBcrypt) || !md5PasswordBcrypt.equals(passwordBcrypt)) {
            LogManager.me().executeLoginLog(sysUser.getAccount(), LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_PWD_ERROR.getMessage());
            // throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR);
            String key = CacheKeyConstant.SYS_USER_LOGIN_ERROR_COUNT + sysUser.getAccount();
            int count = 1;
            if (stringRedisTemplate.hasKey(key)) {
                count = Convert.toInt(stringRedisTemplate.opsForValue().get(key)) + 1;
            }
            if (count == CommonConstant.LOGIN_ERROR_COUNT) {
                stringRedisTemplate.opsForValue().set(disabledKey,
                        count + "", CacheKeyConstant.SYS_USER_DISABLED_LIST_TIME, TimeUnit.SECONDS);
            }
            stringRedisTemplate.opsForValue().set(key, count + "", CacheKeyConstant.SYS_USER_LOGIN_ERROR_COUNT_TIME, TimeUnit.SECONDS);

            // AuthExceptionEnum authExceptionEnum = new AuthExceptionEnum("", "");

            throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR.getCode(),
                    "账号或密码错误，请检查account或password参数连续5次登录失败,将会锁定账号,当前错误" + count + "次");
        }
        Integer sysUserStatus = sysUser.getStatus();

        //验证账号是否被冻结
        if (CommonStatusEnum.DISABLE.getCode().equals(sysUserStatus)) {
            LogManager.me().executeLoginLog(sysUser.getAccount(), LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_FREEZE_ERROR.getMessage());
            throw new AuthException(AuthExceptionEnum.ACCOUNT_FREEZE_ERROR);
        }

        //构造SysLoginUser
        SysLoginUser sysLoginUser = this.genSysLoginUser(sysUser);

        //构造jwtPayLoad
        JwtPayLoad jwtPayLoad = new JwtPayLoad(sysUser.getId(), sysUser.getAccount());

        //生成token
        String token = JwtTokenUtil.generateToken(jwtPayLoad);

        //缓存token与登录用户信息对应, 默认2个小时
        this.cacheLoginUser(jwtPayLoad, sysLoginUser);

        //设置最后登录ip和时间
        sysUser.setLastLoginIp(IpAddressUtil.getIp(HttpServletUtil.getRequest()));
        sysUser.setLastLoginTime(DateTime.now());

        //更新用户登录信息
        sysUserService.updateById(sysUser);

        //登录成功，记录登录日志
        LogManager.me().executeLoginLog(sysUser.getAccount(), LogSuccessStatusEnum.SUCCESS.getCode(), null);

        //登录成功，设置SpringSecurityContext上下文，方便获取用户
        this.setSpringSecurityContextAuthentication(sysLoginUser);

        sysLoginUser.setToken(token);
        return sysLoginUser;
    }
}
