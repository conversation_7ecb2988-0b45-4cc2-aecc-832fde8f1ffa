package com.concise.gen.secondaryindexmanageportrayal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.secondaryindexmanageportrayal.entity.SecondaryIndexManagePortrayal;
import com.concise.gen.secondaryindexmanageportrayal.param.SecondaryIndexManagePortrayalParam;
import com.concise.gen.secondaryindexmanageportrayal.result.IndexTreeNode;

import java.util.List;

/**
 * 二级指标管理信息表service接口
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:21
 */
public interface SecondaryIndexManagePortrayalService extends IService<SecondaryIndexManagePortrayal> {

    /**
     * 查询二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    PageResult<SecondaryIndexManagePortrayal> page(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam);

    /**
     * 二级指标管理信息表列表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    List<SecondaryIndexManagePortrayal> list(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam);

    /**
     * 添加二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    void add(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam);

    /**
     * 删除二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    void delete(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam);

    /**
     * 编辑二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    void edit(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam);

    /**
     * 查看二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    SecondaryIndexManagePortrayal detail(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam);


    List<IndexTreeNode> tree(SecondaryIndexManagePortrayalParam secondaryIndexManagePortrayalParam);
}
