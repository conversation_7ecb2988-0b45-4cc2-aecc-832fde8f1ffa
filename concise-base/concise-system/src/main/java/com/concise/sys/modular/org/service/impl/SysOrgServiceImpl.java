package com.concise.sys.modular.org.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.consts.SymbolConstant;
import com.concise.common.enums.CommonStatusEnum;
import com.concise.common.exception.PermissionException;
import com.concise.common.exception.ServiceException;
import com.concise.common.exception.enums.PermissionExceptionEnum;
import com.concise.common.factory.PageFactory;
import com.concise.common.factory.TreeStringBuildFactory;
import com.concise.common.pojo.node.AntdBaseTreeNode;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.PoiUtil;
import com.concise.core.context.login.LoginContext;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.sys.core.enums.DataScopeTypeEnum;
import com.concise.sys.modular.emp.service.SysEmpExtOrgPosService;
import com.concise.sys.modular.emp.service.SysEmpService;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.enums.SysOrgExceptionEnum;
import com.concise.sys.modular.org.mapper.SysOrgMapper;
import com.concise.sys.modular.org.param.SysOrgParam;
import com.concise.sys.modular.org.service.SysOrgService;
import com.concise.sys.modular.role.service.SysRoleDataScopeService;
import com.concise.sys.modular.user.service.SysUserDataScopeService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 系统组织机构service接口实现类
 *
 * <AUTHOR>
 * @date 2020/3/13 16:02
 */
@Service
public class SysOrgServiceImpl extends ServiceImpl<SysOrgMapper, SysOrg> implements SysOrgService {

    @Resource
    private SysEmpService sysEmpService;

    @Resource
    private SysEmpExtOrgPosService sysEmpExtOrgPosService;

    @Resource
    private SysRoleDataScopeService sysRoleDataScopeService;

    @Resource
    private SysUserDataScopeService sysUserDataScopeService;

    @Override
    public PageResult<SysOrg> page(SysOrgParam sysOrgParam) {
        LambdaQueryWrapper<SysOrg> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotNull(sysOrgParam)) {

            // 根据机构名称模糊查询
            if (ObjectUtil.isNotEmpty(sysOrgParam.getName())) {
                queryWrapper.like(SysOrg::getName, sysOrgParam.getName());
            }

            // 根据机构id查询
            if (ObjectUtil.isNotEmpty(sysOrgParam.getId())) {
                queryWrapper.eq(SysOrg::getId, sysOrgParam.getId());
            }

            // 根据父机构id查询
            if (ObjectUtil.isNotEmpty(sysOrgParam.getPid())) {
                queryWrapper
                        .eq(SysOrg::getId, sysOrgParam.getPid())
                        .or()
                        .like(SysOrg::getPids, sysOrgParam.getPid());
            }
        }

        boolean superAdmin = LoginContextHolder.me().isSuperAdmin();

        // 如果是超级管理员则获取所有组织机构，否则只获取其数据范围的机构数据
        if (!superAdmin) {
            List<String> dataScope = sysOrgParam.getDataScope();
            if (ObjectUtil.isEmpty(dataScope)) {
                return new PageResult<>(new Page<>());
            } else {
                Set<String> dataScopeSet = CollectionUtil.newHashSet(dataScope);
                dataScope.forEach(orgId -> {
                    //此处获取所有的上级节点，放入set，用于构造完整树
                    List<String> parentAndChildIdListWithSelf = this.getParentIdListById(orgId);
                    dataScopeSet.addAll(parentAndChildIdListWithSelf);
                });
                queryWrapper.in(SysOrg::getId, dataScopeSet);
            }
        }

        // 查询启用状态的
        queryWrapper.eq(SysOrg::getStatus, CommonStatusEnum.ENABLE.getCode());
        //根据排序升序排列，序号越小越在前
        queryWrapper.orderByAsc(SysOrg::getSort);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<SysOrg> list(SysOrgParam sysOrgParam) {
        LambdaQueryWrapper<SysOrg> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotNull(sysOrgParam)) {
            //根据父机构id查询
            if (ObjectUtil.isNotEmpty(sysOrgParam.getPid())) {
                queryWrapper.eq(SysOrg::getPid, sysOrgParam.getPid());
            }
        }
        //如果是超级管理员则获取所有组织机构，否则只获取其数据范围的机构数据
        boolean superAdmin = LoginContextHolder.me().isSuperAdmin();
        if (!superAdmin) {
            List<String> dataScope = sysOrgParam.getDataScope();
            if (ObjectUtil.isEmpty(dataScope)) {
                return CollectionUtil.newArrayList();
            } else {
                Set<String> dataScopeSet = CollectionUtil.newHashSet(dataScope);
                dataScope.forEach(orgId -> {
                    //此处获取所有的上级节点，放入set，用于构造完整树
                    List<String> parentAndChildIdListWithSelf = this.getParentIdListById(orgId);
                    dataScopeSet.addAll(parentAndChildIdListWithSelf);
                });
                queryWrapper.in(SysOrg::getId, dataScopeSet);
            }
        }
        queryWrapper.eq(SysOrg::getStatus, CommonStatusEnum.ENABLE.getCode());
        //根据排序升序排列，序号越小越在前
        queryWrapper.orderByAsc(SysOrg::getSort);
        return this.list(queryWrapper);
    }

    @Override
    public void add(SysOrgParam sysOrgParam) {
        //校验参数，检查是否存在相同的名称和编码
        checkParam(sysOrgParam, false);
        //获取父id
        String pid = sysOrgParam.getPid();
        boolean superAdmin = LoginContextHolder.me().isSuperAdmin();
        //如果登录用户不是超级管理员
        if (!superAdmin) {
            //如果新增的机构父id不是0，则进行数据权限校验
            if (!pid.equals(0L)) {
                List<String> dataScope = sysOrgParam.getDataScope();
                //数据范围为空
                if (ObjectUtil.isEmpty(dataScope)) {
                    throw new PermissionException(PermissionExceptionEnum.NO_PERMISSION_OPERATE);
                } else if (!dataScope.contains(pid)) {
                    //所添加的组织机构的父机构不在自己的数据范围内
                    throw new PermissionException(PermissionExceptionEnum.NO_PERMISSION_OPERATE);
                }
            } else {
                //如果新增的机构父id是0，则根本没权限，只有超级管理员能添加父id为0的节点
                throw new PermissionException(PermissionExceptionEnum.NO_PERMISSION_OPERATE);
            }
        }

        SysOrg sysOrg = new SysOrg();
        BeanUtil.copyProperties(sysOrgParam, sysOrg);
        this.fillPids(sysOrg);
        sysOrg.setStatus(CommonStatusEnum.ENABLE.getCode());
        this.save(sysOrg);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<SysOrgParam> sysOrgParamList) {
        sysOrgParamList.forEach(sysOrgParam -> {
            SysOrg sysOrg = this.querySysOrg(sysOrgParam);
            String id = sysOrg.getId();
            boolean superAdmin = LoginContextHolder.me().isSuperAdmin();
            if (!superAdmin) {
                List<String> dataScope = sysOrgParam.getDataScope();
                //数据范围为空
                if (ObjectUtil.isEmpty(dataScope)) {
                    throw new PermissionException(PermissionExceptionEnum.NO_PERMISSION_OPERATE);
                } else if (!dataScope.contains(id)) {
                    //所操作的数据不在自己的数据范围内
                    throw new PermissionException(PermissionExceptionEnum.NO_PERMISSION_OPERATE);
                }
            }
            // 该机构下有员工，则不能删
            boolean hasOrgEmp = sysEmpService.hasOrgEmp(id);
            if (hasOrgEmp) {
                throw new ServiceException(SysOrgExceptionEnum.ORG_CANNOT_DELETE);
            }

            // 该附属机构下若有员工，则不能删除
            boolean hasExtOrgEmp = sysEmpExtOrgPosService.hasExtOrgEmp(id);
            if (hasExtOrgEmp) {
                throw new ServiceException(SysOrgExceptionEnum.ORG_CANNOT_DELETE);
            }

            // 级联删除子节点
            List<String> childIdList = this.getChildIdListById(id);
            childIdList.add(id);
            LambdaUpdateWrapper<SysOrg> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(SysOrg::getId, childIdList)
                    .set(SysOrg::getStatus, CommonStatusEnum.DELETED.getCode());
            this.update(updateWrapper);

            // 级联删除该机构及子机构对应的角色-数据范围关联信息
            sysRoleDataScopeService.deleteRoleDataScopeListByOrgIdList(childIdList);

            // 级联删除该机构子机构对应的用户-数据范围关联信息
            sysUserDataScopeService.deleteUserDataScopeListByOrgIdList(childIdList);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(SysOrgParam sysOrgParam) {

        SysOrg sysOrg = this.querySysOrg(sysOrgParam);
        String id = sysOrg.getId();

        // 检测此人数据范围能不能操作这个公司
        boolean superAdmin = LoginContextHolder.me().isSuperAdmin();
        if (!superAdmin) {
            List<String> dataScope = sysOrgParam.getDataScope();
            //数据范围为空
            if (ObjectUtil.isEmpty(dataScope)) {
                throw new PermissionException(PermissionExceptionEnum.NO_PERMISSION_OPERATE);
            }
            //数据范围中不包含本公司
            else if (!dataScope.contains(id)) {
                throw new PermissionException(PermissionExceptionEnum.NO_PERMISSION_OPERATE);
            }
        }

        //校验参数，检查是否存在相同的名称和编码
        checkParam(sysOrgParam, true);

        //如果名称有变化，则修改对应员工的机构相关信息
        if (!sysOrg.getName().equals(sysOrgParam.getName())) {
            sysEmpService.updateEmpOrgInfo(sysOrg.getId(), sysOrg.getName());
        }

        BeanUtil.copyProperties(sysOrgParam, sysOrg);
        this.fillPids(sysOrg);

        //不能修改状态，用修改状态接口修改状态
        sysOrg.setStatus(null);
        this.updateById(sysOrg);
        //将所有子的父id进行更新
        List<String> childIdListById = this.getChildIdListById(sysOrg.getId());
        childIdListById.forEach(subChildId -> {
            SysOrg child = this.getById(subChildId);
            SysOrgParam childParam = new SysOrgParam();
            BeanUtil.copyProperties(child, childParam);
            this.edit(childParam);
        });
    }

    @Override
    public SysOrg detail(SysOrgParam sysOrgParam) {
        return this.querySysOrg(sysOrgParam);
    }

    @Override
    public List<AntdBaseTreeNode> tree(SysOrgParam sysOrgParam) {
        List<AntdBaseTreeNode> treeNodeList = CollectionUtil.newArrayList();
        LambdaQueryWrapper<SysOrg> queryWrapper = new LambdaQueryWrapper<>();

        // 如果是超级管理员则获取所有组织机构，否则只获取其数据范围的机构数据
        LoginContext loginContext = LoginContextHolder.me();
        boolean superAdmin = loginContext.isSuperAdmin();
        String deptId = "";
        if (!superAdmin) {
            //非管理员查询本级及下级数据
            String sysLoginUserOrgId = loginContext.getSysLoginUserOrgId();
            SysOrg sysOrg = this.getById(sysLoginUserOrgId);
            deptId = sysOrg.getId();
            //判断登录人机构是否是部门，如果是部门则需要再往上找一级(参照信息化平台 type = sp 表示该组织机构是部门)
            if ("sp".equals(sysOrg.getType())) {
                deptId = sysOrg.getPid();
            }
            Set<String> deptIds = new HashSet<>();
            // 查询本级&下级
            deptIds = new HashSet<String>(1) {{
                add(sysOrg.getId());
            }};
            getSubOrgIdById(deptId, deptIds, list());
            deptIds.add(deptId);
            queryWrapper.in(SysOrg::getId, deptIds);
        }

        // 只查询未删除的
        queryWrapper.eq(SysOrg::getStatus, CommonStatusEnum.ENABLE.getCode());
        if (ObjectUtil.isNotEmpty(sysOrgParam.getSp())&&"1".equals(sysOrgParam.getSp())){
            queryWrapper.isNull(SysOrg::getType);
        }

        //根据排序升序排列，序号越小越在前
        queryWrapper.orderByAsc(SysOrg::getSort);

        List<SysOrg> list = this.list(queryWrapper);
        if (list.size() == 1) {
            //如果登录人所在机构是司法所一级， 则手动设置parentId 及 pid 为0
            AntdBaseTreeNode orgTreeNode = new AntdBaseTreeNode();
            orgTreeNode.setId(list.get(0).getId());
            orgTreeNode.setParentId("0");
            orgTreeNode.setTitle(list.get(0).getName());
            orgTreeNode.setValue(list.get(0).getId());
            orgTreeNode.setWeight(list.get(0).getSort());
            treeNodeList.add(orgTreeNode);
        } else {
            for (SysOrg sysOrg : list) {
                AntdBaseTreeNode orgTreeNode = new AntdBaseTreeNode();
                orgTreeNode.setId(sysOrg.getId());
                //若deptId不为空, 需将顶级机构ParentId设置为0
                if (StringUtils.isNotEmpty(deptId) && sysOrg.getId().equals(deptId)) {
                    orgTreeNode.setParentId("0");
                } else {
                    orgTreeNode.setParentId(sysOrg.getPid());
                }
                orgTreeNode.setTitle(sysOrg.getName());
                orgTreeNode.setValue(String.valueOf(sysOrg.getId()));
                orgTreeNode.setWeight(sysOrg.getSort());
                treeNodeList.add(orgTreeNode);
            }
        }

//        log.error("treeNodeList  " + JSON.toJSONString(treeNodeList));

        return new TreeStringBuildFactory<AntdBaseTreeNode>().doTreeBuild(treeNodeList);
    }

    @Override
    public List<String> getDataScopeListByDataScopeType(Integer dataScopeType, String orgId) {
        List<String> resultList = CollectionUtil.newArrayList();

        if (ObjectUtil.isEmpty(orgId)) {
            return CollectionUtil.newArrayList();
        }

        // 如果是范围类型是全部数据，则获取当前系统所有的组织架构id
        if (DataScopeTypeEnum.ALL.getCode().equals(dataScopeType)) {
            resultList = this.getOrgIdAll();
        }
        // 如果范围类型是本部门及以下部门，则查询本节点和子节点集合，包含本节点
        else if (DataScopeTypeEnum.DEPT_WITH_CHILD.getCode().equals(dataScopeType)) {
            resultList = this.getChildIdListWithSelfById(orgId);
        }
        // 如果数据范围是本部门，不含子节点，则直接返回本部门
        else if (DataScopeTypeEnum.DEPT.getCode().equals(dataScopeType)) {
            resultList.add(orgId);
        }

        return resultList;
    }

    /**
     * 根据条件获取组织机构id集合
     *
     * <AUTHOR>
     * @date 2020/4/5 18:35
     */
    private List<String> getOrgIdAll() {
        List<String> resultList = CollectionUtil.newArrayList();

        LambdaQueryWrapper<SysOrg> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(SysOrg::getStatus, CommonStatusEnum.ENABLE.getCode());

        this.list(queryWrapper).forEach(sysOrg -> resultList.add(sysOrg.getId()));
        return resultList;
    }

    /**
     * 校验参数，检查是否存在相同的名称和编码
     *
     * <AUTHOR>
     * @date 2020/3/25 21:23
     */
    private void checkParam(SysOrgParam sysOrgParam, boolean isExcludeSelf) {
        String id = sysOrgParam.getId();
        String name = sysOrgParam.getName();
        String code = sysOrgParam.getCode();
        String pid = sysOrgParam.getPid();

        //如果父id不是根节点
        if (!pid.equals(0L)) {
            SysOrg pOrg = this.getById(pid);
            if (ObjectUtil.isNull(pOrg)) {
                //父机构不存在
                throw new ServiceException(SysOrgExceptionEnum.ORG_NOT_EXIST);
            }
        }

        // 如果是编辑，父id和自己的id不能一致
        if (isExcludeSelf) {
            if (sysOrgParam.getId().equals(sysOrgParam.getPid())) {
                throw new ServiceException(SysOrgExceptionEnum.ID_CANT_EQ_PID);
            }

            // 如果是编辑，父id不能为自己的子节点
            List<String> childIdListById = this.getChildIdListById(sysOrgParam.getId());
            if (ObjectUtil.isNotEmpty(childIdListById)) {
                if (childIdListById.contains(sysOrgParam.getPid())) {
                    throw new ServiceException(SysOrgExceptionEnum.PID_CANT_EQ_CHILD_ID);
                }
            }
        }

        LambdaQueryWrapper<SysOrg> queryWrapperByName = new LambdaQueryWrapper<>();
        queryWrapperByName.eq(SysOrg::getName, name)
                .ne(SysOrg::getStatus, CommonStatusEnum.DELETED.getCode());

        LambdaQueryWrapper<SysOrg> queryWrapperByCode = new LambdaQueryWrapper<>();
        queryWrapperByCode.eq(SysOrg::getCode, code)
                .ne(SysOrg::getStatus, CommonStatusEnum.DELETED.getCode());

        if (isExcludeSelf) {
            queryWrapperByName.ne(SysOrg::getId, id);
            queryWrapperByCode.ne(SysOrg::getId, id);
        }
        int countByName = this.count(queryWrapperByName);
        int countByCode = this.count(queryWrapperByCode);

        if (countByName >= 1) {
            throw new ServiceException(SysOrgExceptionEnum.ORG_NAME_REPEAT);
        }
        if (countByCode >= 1) {
            throw new ServiceException(SysOrgExceptionEnum.ORG_CODE_REPEAT);
        }
    }

    /**
     * 获取系统组织机构
     *
     * <AUTHOR>
     * @date 2020/3/26 9:56
     */
    private SysOrg querySysOrg(SysOrgParam sysOrgParam) {
        SysOrg sysOrg = this.getById(sysOrgParam.getId());
        if (ObjectUtil.isNull(sysOrg)) {
            throw new ServiceException(SysOrgExceptionEnum.ORG_NOT_EXIST);
        }
        return sysOrg;
    }

    /**
     * 填充父ids
     *
     * <AUTHOR>
     * @date 2020/3/26 11:28
     */
    private void fillPids(SysOrg sysOrg) {
        if (sysOrg.getPid().equals(0L)) {
            sysOrg.setPids(SymbolConstant.LEFT_SQUARE_BRACKETS +
                    0 +
                    SymbolConstant.RIGHT_SQUARE_BRACKETS +
                    SymbolConstant.COMMA);
        } else {
            //获取父组织机构
            SysOrg pSysOrg = this.getById(sysOrg.getPid());
            sysOrg.setPids(pSysOrg.getPids() +
                    SymbolConstant.LEFT_SQUARE_BRACKETS + pSysOrg.getId() +
                    SymbolConstant.RIGHT_SQUARE_BRACKETS +
                    SymbolConstant.COMMA);
        }
    }

    /**
     * 根据节点id获取所有子节点id集合
     *
     * <AUTHOR>
     * @date 2020/3/26 11:31
     */
    private List<String> getChildIdListById(String id) {
        List<String> childIdList = CollectionUtil.newArrayList();
        LambdaQueryWrapper<SysOrg> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.like(SysOrg::getPids, SymbolConstant.LEFT_SQUARE_BRACKETS + id +
                SymbolConstant.RIGHT_SQUARE_BRACKETS);

        this.list(queryWrapper).forEach(sysOrg -> childIdList.add(sysOrg.getId()));

        return childIdList;
    }

    /**
     * 根据节点id获取所有父节点id集合，不包含自己
     *
     * <AUTHOR>
     * @date 2020/4/6 14:53
     */
    private List<String> getParentIdListById(String id) {
        List<String> resultList = CollectionUtil.newArrayList();
        SysOrg sysOrg = this.getById(id);
        String pids = sysOrg.getPids();
        String pidsWithRightSymbol = StrUtil.removeAll(pids, SymbolConstant.LEFT_SQUARE_BRACKETS);
        String pidsNormal = StrUtil.removeAll(pidsWithRightSymbol, SymbolConstant.RIGHT_SQUARE_BRACKETS);
        String[] pidsNormalArr = pidsNormal.split(SymbolConstant.COMMA);
        for (String pid : pidsNormalArr) {
            resultList.add(pid);
        }
        return resultList;
    }

    /**
     * 根据节点id获取所有子节点id集合，包含自己
     *
     * <AUTHOR>
     * @date 2020/4/6 14:54
     */
    private List<String> getChildIdListWithSelfById(String id) {
        List<String> childIdListById = this.getChildIdListById(id);
        List<String> resultList = CollectionUtil.newArrayList(childIdListById);
        resultList.add(id);
        return resultList;
    }

    /**
     * 根据节点id获取父节点和子节点id集合，包含自己
     *
     * <AUTHOR>
     * @date 2020/4/7 16:50
     */
    private List<String> getParentAndChildIdListWithSelfById(String id) {
        Set<String> resultSet = CollectionUtil.newHashSet();
        List<String> parentIdListById = this.getParentIdListById(id);
        List<String> childIdListById = this.getChildIdListWithSelfById(id);
        resultSet.addAll(parentIdListById);
        resultSet.addAll(childIdListById);
        return CollectionUtil.newArrayList(resultSet);
    }

    @Override
    public void export(SysOrgParam sysOrgParam) {
        List<SysOrg> list = this.list(sysOrgParam);
        PoiUtil.exportExcelWithStream("SysOrg.xls", SysOrg.class, list);
    }

    @Override
    @Cacheable(value = "getDeptIds", key = "#deptId")
    public Set<String> getDeptIds(String deptId) {
        SysOrg sysOrg = getById(deptId);
        if (null != sysOrg) {
            //判断登录人机构是否是部门，如果是部门则需要再往上找一级( Remark = sp 表示该组织机构是部门)
            if ("sp".equals(sysOrg.getType())) {
                deptId = sysOrg.getPid();
            }
        }
        Set<String> orgs = null;
        if (ObjectUtil.isNotEmpty(deptId)) {
            orgs = new HashSet<>();
            // 查询本级&下级
            getSubOrgIdById(deptId, orgs, list());
            orgs.add(deptId);
        }
        return orgs;
    }

    public Set<String> getSubOrgIdById(String orgId, Set<String> orgs, List<SysOrg> list) {
        if (list != null && list.size() > 0) {
            List<SysOrg> collect = list.stream().filter(m -> orgId.equals(m.getPid())).collect(Collectors.toList());
            if (collect.size() > 0 && collect.get(0) != null) {
                // 获取子部门id
                List<String> ids = list.stream().filter(m -> orgId.equals(m.getPid())).map(SysOrg::getId).collect(Collectors.toList());
                orgs.addAll(ids);
                for (String id : ids) {
                    getSubOrgIdById(id, orgs, list);
                }
            }
        }
        return orgs;
    }

    @Override
    public List<SysOrg> getByPid(String pid) {
        LambdaQueryWrapper<SysOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOrg::getPid, pid);
        return this.list(queryWrapper);
    }

    @Override
    public SysOrg zllxFilter(String deptId) {
        String pid = this.baseMapper.zllxFilter(deptId);
        return this.getById(pid);
    }

    @Override
    public List<String> getDataScopeListByUserOrgIdAndSearchOrgId(String userOrgId, String searchOrgId) {
        List<String> dataScopeList = LoginContextHolder.me().getLoginUserDataScopeIdList();

        //如果搜索机构为空，直接返回用户权限范围数据
        if (ObjectUtil.isEmpty(searchOrgId)) {
            return dataScopeList;
        }
        //如果用户单位为空，且非管理员，返回空集合
        if (ObjectUtil.isEmpty(userOrgId) && !LoginContextHolder.me().isSuperAdmin()) {
            return new ArrayList<>();
        }
        //搜索机构不为空，判断搜索机构是否在权限范围内，在权限范围内，返回搜索机构的本级及下级，不在权限范围内，返回数据权限内的数据
        if (dataScopeList.contains(searchOrgId)) {
            return getDeptIds(searchOrgId).stream().distinct().collect(Collectors.toList());
        }
        return dataScopeList;
    }

    @Override
    public <E> void buildDataScope(QueryWrapper<E> queryWrapper, String jzjg,String field) {
        //管理员，忽略自身权限，专注搜索机构权限
        if (LoginContextHolder.me().isSuperAdmin()) {
            if (ObjectUtil.isNotEmpty(jzjg)) {
                queryWrapper.in(field, getDeptIds(jzjg));
            }
        } else {
            //非管理员
            List<String> dataScopeList = getDataScopeListByUserOrgIdAndSearchOrgId(LoginContextHolder.me().getSysLoginUserOrgId(), jzjg);
            if (ObjectUtil.isNotEmpty(dataScopeList)) {
                queryWrapper.in(field, dataScopeList);
            }
        }
    }
}
