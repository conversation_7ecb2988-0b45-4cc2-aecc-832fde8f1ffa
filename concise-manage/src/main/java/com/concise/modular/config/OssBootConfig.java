package com.concise.modular.config;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import com.concise.common.file.util.OssBootUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 阿里云OSS配置类，用于初始化OssBootUtil
 */
@Slf4j
@Configuration
public class OssBootConfig {

    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    @Value("${aliyun.oss.accessKey}")
    private String accessKey;

    @Value("${aliyun.oss.secretKey}")
    private String secretKey;

    @Value("${aliyun.oss.bucketName}")
    private String bucketName;

    @Value("${aliyun.oss.folder}")
    private String folder;

    @Value("${aliyun.oss.staticDomain}")
    private String staticDomain;

    @Value("${aliyun.oss.publicDomain}")
    private String publicDomain;

    /**
     * 初始化OssBootUtil
     */
    @PostConstruct
    public void init() {
        log.info("开始初始化OssBootUtil，Endpoint: {}, BucketName: {}", endpoint, bucketName);
        OssBootUtil.setEndPoint(endpoint);
        OssBootUtil.setAccessKeyId(accessKey);
        OssBootUtil.setAccessKeySecret(secretKey);
        OssBootUtil.setBucketName(bucketName);
        OssBootUtil.setStaticDomain(staticDomain);
        OssBootUtil.setPublicDomain(publicDomain);
        log.info("OssBootUtil初始化完成");
    }
} 