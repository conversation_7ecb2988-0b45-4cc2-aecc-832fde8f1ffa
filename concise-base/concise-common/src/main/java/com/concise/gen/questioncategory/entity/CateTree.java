package com.concise.gen.questioncategory.entity;

import lombok.Data;

import java.util.List;

/**
 * 功能描述：
 *
 * @Author: xxx
 * @Date: 2020/11/11 12:00
 */
@Data
public class CateTree {
    //分类id
    private String id;

    private String key;

    private String value;
    //父id
    private String parentId;
    //分类名
    private String typeName;

    private String depId;
    private String title;
    //排序
    private Integer indexId;
    //等级
    private String rank;
    //子分类集合
    private List<CateTree> children;

    public void setId(String id) {
        this.id = id;
        this.key=id;
        this.value=id;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
        this.title=typeName;
    }
}
