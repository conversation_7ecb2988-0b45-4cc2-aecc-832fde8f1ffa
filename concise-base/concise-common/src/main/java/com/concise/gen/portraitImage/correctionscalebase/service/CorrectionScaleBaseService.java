package com.concise.gen.portraitImage.correctionscalebase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.portraitImage.correctionscalebase.entity.CorrectionScaleBase;
import com.concise.gen.portraitImage.correctionscalebase.param.CorrectionScaleBaseParam;

import java.util.List;

/**
 * 量表配置基本信息表service接口
 *
 * <AUTHOR>
 * @date 2022-11-04 10:51:21
 */
public interface CorrectionScaleBaseService extends IService<CorrectionScaleBase> {

    /**
     * 查询量表配置基本信息表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:21
     */
    PageResult<CorrectionScaleBase> page(CorrectionScaleBaseParam correctionScaleBaseParam);

    /**
     * 量表配置基本信息表列表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:21
     */
    List<CorrectionScaleBase> list(CorrectionScaleBaseParam correctionScaleBaseParam);

    /**
     * 添加量表配置基本信息表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:21
     */
    void add(CorrectionScaleBaseParam correctionScaleBaseParam);

    /**
     * 删除量表配置基本信息表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:21
     */
    void delete(CorrectionScaleBaseParam correctionScaleBaseParam);

    /**
     * 编辑量表配置基本信息表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:21
     */
    void edit(CorrectionScaleBaseParam correctionScaleBaseParam);

    /**
     * 查看量表配置基本信息表
     *
     * <AUTHOR>
     * @date 2022-11-04 10:51:21
     */
     CorrectionScaleBase detail(CorrectionScaleBaseParam correctionScaleBaseParam);
}
