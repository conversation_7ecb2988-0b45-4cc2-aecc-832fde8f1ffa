package com.concise.gen.secondaryindexmanage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.secondaryindexmanage.entity.SecondaryIndexManage;
import com.concise.gen.secondaryindexmanage.param.SecondaryIndexManageParam;
import com.concise.gen.secondaryindexmanage.result.IndexTreeNode;

import java.util.List;

/**
 * 二级指标管理信息表service接口
 *
 * <AUTHOR>
 * @date 2022-05-12 10:02:21
 */
public interface SecondaryIndexManageService extends IService<SecondaryIndexManage> {

    /**
     * 查询二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    PageResult<SecondaryIndexManage> page(SecondaryIndexManageParam secondaryIndexManageParam);

    /**
     * 二级指标管理信息表列表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    List<SecondaryIndexManage> list(SecondaryIndexManageParam secondaryIndexManageParam);

    /**
     * 添加二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    void add(SecondaryIndexManageParam secondaryIndexManageParam);

    /**
     * 删除二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    void delete(SecondaryIndexManageParam secondaryIndexManageParam);

    /**
     * 编辑二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
    void edit(SecondaryIndexManageParam secondaryIndexManageParam);

    /**
     * 查看二级指标管理信息表
     *
     * <AUTHOR>
     * @date 2022-05-12 10:02:21
     */
     SecondaryIndexManage detail(SecondaryIndexManageParam secondaryIndexManageParam);


    List<IndexTreeNode> tree(SecondaryIndexManageParam secondaryIndexManageParam);
}
