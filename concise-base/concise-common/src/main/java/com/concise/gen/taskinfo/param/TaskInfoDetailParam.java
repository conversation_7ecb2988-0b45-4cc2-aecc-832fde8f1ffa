package com.concise.gen.taskinfo.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 任务详情表参数类
 *
 * <AUTHOR>
 * @date 2022-02-28 15:17:32
*/
@Data
public class TaskInfoDetailParam extends BaseParam {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 任务信息ID
     */
    @NotNull(message = "任务信息ID不能为空，请检查taskInfoId参数", groups = {list.class, page.class})
    private String taskInfoId;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 通话话术ID
     */
    private String dictionId;

    /**
     * 通话标题
     */
    private String dictionTitle;

    /**
     * 矫正机构ID
     */
    private String jzjg;
    private String jzjgId;

    /**
     * 矫正机构名称
     */
    @NotBlank(message = "矫正机构名称不能为空，请检查jzjgName参数", groups = {add.class, edit.class})
    private String jzjgName;

    /**
     * 矫正人员姓名
     */
    @NotBlank(message = "矫正人员姓名不能为空，请检查sqjzryName参数", groups = {add.class, edit.class})
    private String sqjzryName;
    private String xm;
    /**
     * 社区矫正人员id
     */
    private String sqjzryId;

    /**
     * 矫正人员个人联系电话
     */
    @NotBlank(message = "矫正人员个人联系电话不能为空，请检查sqjzryTel参数", groups = {add.class, edit.class})
    private String sqjzryTel;
    private String grlxdh;

    /**
     * 呼叫时间
     */
    private String startTime;

    /**
     * 挂机时间
     */
    private String endTime;

    /**
     * 全程通话音频http地址
     */
    private String audio;

    /**
     * 交互记录
     */
    private String interact;

    /**
     * 外呼结果
     */
    private String callRet;

    /**
     * 通话音频质量
     */
    private String audioQuality;

    /**
     * 声音校验结果
     */
    private String audioCheck;

    private String platTaskId;

    /**
     * 同步状态
     */
    private String sync;

    private String warningType;
    private String warningResult;
    private String warningExtendId;


}
